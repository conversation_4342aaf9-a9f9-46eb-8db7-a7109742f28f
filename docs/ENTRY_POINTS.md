# Spark MCP Server - Entry Points Guide

## Overview

The Spark MCP Server has two different entry points designed for different use cases:

- **`main_new.py`** - Production MCP server with pure MCP protocol
- **`main_dev.py`** - Development wrapper with HTTP endpoints for testing

## Entry Point Comparison

| Feature | main_new.py | main_dev.py |
|---------|-------------|-------------|
| **Purpose** | Production MCP server | Development testing |
| **Protocol** | Pure MCP over SSE/stdio | HTTP REST + MCP wrapper |
| **Transport** | SSE or stdio | HTTP (FastAPI) |
| **Endpoints** | MCP tools only | HTTP REST + MCP tools |
| **Hot Reload** | No | Yes (uvicorn --reload) |
| **Use Case** | MCP clients | Development/debugging |

## main_new.py - Production Server

### Purpose
- Pure MCP server implementation
- Designed for MCP client connections
- Production-ready with full memory system

### Features
- ✅ Full MCP protocol support
- ✅ SSE and stdio transports
- ✅ All memory tools (add_memories, search_memory, etc.)
- ✅ Performance monitoring
- ✅ Health checks via MCP tools
- ✅ Two-phase memory pipeline
- ✅ BGE embeddings integration

### Usage
```bash
# Direct execution
python src/main_new.py

# Docker production
docker-compose up spark-mcp

# Environment variables
TRANSPORT=sse  # or stdio
HOST=0.0.0.0
PORT=8050
```

### MCP Client Configuration
```json
{
  "spark-memory": {
    "command": "npx",
    "args": ["-y", "mcp-remote", "http://your-server:8050/sse/", "--allow-http"]
  }
}
```

## main_dev.py - Development Server

### Purpose
- Development wrapper around main_new.py
- Provides HTTP endpoints for easy testing
- Enables hot reload during development

### Features
- ✅ HTTP REST endpoints
- ✅ FastAPI integration
- ✅ Hot reload support
- ✅ Development logging
- ✅ Health check endpoint
- ✅ Tools listing endpoint
- ✅ MCP server integration

### Usage
```bash
# Direct execution
python src/main_dev.py

# Docker development
docker-compose -f docker-compose.dev.yml up

# Environment variables
ENVIRONMENT=development
PORT=8050
```

### HTTP Endpoints
- `GET /` - Root health check
- `GET /health` - Detailed health status
- `GET /tools` - List available MCP tools
- `POST /mcp` - MCP SSE endpoint (simplified)

## When to Use Which

### Use main_new.py when:
- ✅ Connecting with MCP clients (Claude Desktop, etc.)
- ✅ Production deployment
- ✅ Need full MCP protocol support
- ✅ Performance testing with MCP tools

### Use main_dev.py when:
- ✅ Development and debugging
- ✅ Testing HTTP endpoints
- ✅ Need hot reload functionality
- ✅ Quick health checks via curl
- ✅ Exploring available tools

## Docker Configuration

### Development (docker-compose.dev.yml)
```yaml
command: ["uv", "run", "--no-dev", "python", "src/main_dev.py"]
```

### Production (docker-compose.yml)
```yaml
command: ["uv", "run", "src/main_new.py"]
```

## Troubleshooting

### Issue: "SSE Endpoint 404"
- **Cause**: Using main_new.py but expecting HTTP endpoints
- **Solution**: Use main_dev.py for development with HTTP endpoints

### Issue: "Main function not executing"
- **Cause**: Import errors or port conflicts
- **Solution**: Check logs and ensure ports are available

### Issue: "Tool execution errors"
- **Cause**: Trying to access MCP tools via HTTP in main_dev.py
- **Solution**: Use MCP client for tool execution, HTTP endpoints are for status only

## Best Practices

1. **Development**: Use main_dev.py with docker-compose.dev.yml
2. **Production**: Use main_new.py with docker-compose.yml
3. **Testing**: Use main_dev.py for quick HTTP tests, main_new.py for MCP protocol tests
4. **Debugging**: Check logs in both entry points to understand initialization issues
