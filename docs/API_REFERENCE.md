# Spark Memory MCP Server - API Reference

## Overview

This document provides comprehensive API reference for the Spark Memory MCP Server. The server implements 12 MCP tools for memory operations, performance monitoring, and system health checks.

## Base Configuration

### MCP Client Setup
```json
{
  "spark-memory": {
    "command": "npx",
    "args": [
      "-y",
      "mcp-remote",
      "http://your-server-ip:8050/sse/",
      "--allow-http"
    ],
    "env": {
      "MCP_USER_ID": "your-user-id"
    }
  }
}
```

### Authentication
The server uses user-based isolation through the `MCP_USER_ID` environment variable or explicit `user_id` parameters in tool calls.

## Core Memory Operations

### 1. add_memories

Add new memories using the two-phase pipeline (extraction → update).

**Parameters:**
- `text` (string, required): The text content to extract memories from
- `user_id` (string, optional): User identifier for memory isolation

**Example:**
```json
{
  "name": "add_memories",
  "arguments": {
    "text": "Our team uses PostgreSQL 15 with pgvector for semantic search. The database runs on port 5432.",
    "user_id": "dev-team"
  }
}
```

**Response:**
```json
{
  "success": true,
  "operation": "ADD",
  "memories_added": 2,
  "pipeline_performance": {
    "extraction_time_ms": 1240,
    "update_time_ms": 890,
    "total_time_ms": 2130
  },
  "extracted_memories": [
    "Team uses PostgreSQL 15 with pgvector for semantic search",
    "Database runs on port 5432"
  ]
}
```

### 2. search_memory

Search memories using BGE vector similarity.

**Parameters:**
- `query` (string, required): Search query
- `limit` (integer, optional, default: 5): Maximum number of results
- `user_id` (string, optional): User identifier for memory isolation

**Example:**
```json
{
  "name": "search_memory",
  "arguments": {
    "query": "database configuration",
    "limit": 3,
    "user_id": "dev-team"
  }
}
```

**Response:**
```json
{
  "success": true,
  "results": [
    {
      "id": "mem_123",
      "content": "Team uses PostgreSQL 15 with pgvector for semantic search",
      "similarity_score": 0.89,
      "created_at": "2024-08-02T10:15:30Z",
      "updated_at": "2024-08-02T10:15:30Z"
    },
    {
      "id": "mem_124",
      "content": "Database runs on port 5432",
      "similarity_score": 0.76,
      "created_at": "2024-08-02T10:15:30Z",
      "updated_at": "2024-08-02T10:15:30Z"
    }
  ],
  "query_time_ms": 45,
  "total_memories": 150
}
```

### 3. list_memories

List all memories for a user with pagination.

**Parameters:**
- `limit` (integer, optional, default: 50): Maximum number of memories to return
- `user_id` (string, optional): User identifier for memory isolation

**Example:**
```json
{
  "name": "list_memories",
  "arguments": {
    "limit": 10,
    "user_id": "dev-team"
  }
}
```

**Response:**
```json
{
  "success": true,
  "memories": [
    {
      "id": "mem_123",
      "content": "Team uses PostgreSQL 15 with pgvector for semantic search",
      "created_at": "2024-08-02T10:15:30Z",
      "updated_at": "2024-08-02T10:15:30Z"
    }
  ],
  "total_count": 150,
  "returned_count": 10
}
```

### 4. delete_all_memories

Delete all memories for a user (use with caution).

**Parameters:**
- `user_id` (string, optional): User identifier for memory isolation

**Example:**
```json
{
  "name": "delete_all_memories",
  "arguments": {
    "user_id": "dev-team"
  }
}
```

**Response:**
```json
{
  "success": true,
  "deleted_count": 150,
  "message": "All memories deleted for user: dev-team"
}
```

## Performance Monitoring

### 5. get_performance_stats

Get current performance statistics and LOCOMO target comparison.

**Parameters:** None

**Example:**
```json
  {
    "name": "get_performance_stats",
    "arguments": {}
  }
```

**Response:**
```json
{
  "success": true,
  "performance_stats": {
    "pipeline_performance": {
      "p50_latency_ms": 650,
      "p95_latency_ms": 1200,
      "avg_latency_ms": 780,
      "total_operations": 1500
    },
    "bge_performance": {
      "avg_response_time_ms": 45,
      "success_rate": 0.998,
      "total_requests": 2800
    },
    "database_performance": {
      "avg_query_time_ms": 25,
      "connection_pool_usage": 0.60
    },
    "locomo_targets": {
      "p50_target_ms": 708,
      "p95_target_ms": 1440,
      "target_compliance": {
        "p50": "PASS",
        "p95": "PASS"
      }
    }
  }
}
```

### 6. get_conversation_summary

Get current conversation summary for a user.

**Parameters:**
- `user_id` (string, optional): User identifier for memory isolation

**Example:**
```json
{
  "name": "get_conversation_summary",
  "arguments": {
    "user_id": "dev-team"
  }
}
```

**Response:**
```json
{
    "success": true,
    "summary": {
        "current_summary": "Team is working on MCP memory server with PostgreSQL backend...",
        "summary_length": 245,
        "last_updated": "2024-08-02T14:30:15Z",
        "message_count": 15
    }
}
```

## BGE Embedding Operations

### 7. get_bge_health_status

Get comprehensive BGE embedding health status and performance metrics.

**Parameters:** None

**Example:**
```json
{
  "name": "get_bge_health_status",
  "arguments": {}
}
```

**Response:**
```json
{
  "success": true,
  "bge_status": {
    "server_url": "http://192.168.1.84:8080",
    "health": "HEALTHY",
    "response_time_ms": 42,
    "embedding_dimension": 768,
    "model": "BAAI/bge-base-en-v1.5",
    "performance_metrics": {
      "avg_response_time_ms": 45,
      "success_rate": 0.998,
      "total_requests": 2800,
      "last_error": null
    }
  }
}
```

### 8. benchmark_bge_performance

Benchmark BGE embedding performance with test data.

**Parameters:**
- `test_texts` (array of strings, optional): Custom test texts for benchmarking

**Example:**
```json
{
  "name": "benchmark_bge_performance",
  "arguments": {
    "test_texts": [
      "Database performance optimization",
      "Memory management in PostgreSQL",
      "Vector similarity search"
    ]
  }
}
```

**Response:**
```json
{
    "success": true,
    "benchmark_results": {
        "total_tests": 3,
        "avg_response_time_ms": 43,
        "min_response_time_ms": 38,
        "max_response_time_ms": 52,
        "success_rate": 1.0,
        "embedding_dimension": 768,
        "throughput_per_second": 23.2
    }
}
```

## Cache Management

### 9. get_cache_performance_report

Get comprehensive cache performance report with optimization insights.

**Parameters:** None

**Example:**
```json
{
  "name": "get_cache_performance_report",
  "arguments": {}
}
```

**Response:**
```json
{
    "success": true,
    "cache_performance": {
        "memory_cache": {
            "hit_rate": 0.85,
            "miss_rate": 0.15,
            "eviction_rate": 0.02,
            "memory_usage_mb": 45.2
        },
        "llm_cache": {
            "hit_rate": 0.78,
            "total_requests": 1200,
            "cache_size": 890,
            "avg_response_time_ms": 25
        },
        "optimization_insights": [
            "Memory cache performing well with 85% hit rate",
            "Consider increasing cache size for better performance"
        ]
    }
}
```

### 10. invalidate_llm_cache

Invalidate LLM cache entries for testing and optimization.

**Parameters:**
- `cache_type` (string, optional): Type of cache to invalidate ("memory", "llm", or null for all)

**Example:**
```json
{
  "name": "invalidate_llm_cache",
  "arguments": {
    "cache_type": "llm"
  }
}
```

**Response:**
```json
{
    "success": true,
    "invalidated_entries": 245,
    "cache_type": "llm",
    "message": "LLM cache successfully invalidated"
}
```

### 11. cache_warm_common_patterns

Warm cache with common memory extraction and decision patterns.

**Parameters:** None

**Example:**
```json
{
  "name": "cache_warm_common_patterns",
  "arguments": {}
}
```

**Response:**
```json
{
    "success": true,
    "warming_results": {
        "patterns_cached": 15,
        "cache_entries_created": 45,
        "warming_time_ms": 2340,
        "estimated_performance_improvement": "15-25%"
    }
}
```

## System Health

### 12. health_check

Comprehensive health check for Docker container monitoring.

**Parameters:** None

**Example:**
```json
{
  "name": "health_check",
  "arguments": {}
}
```

**Response:**
```json
{
    "success": true,
    "health_status": {
        "overall_status": "HEALTHY",
        "components": {
            "mcp_server": "HEALTHY",
            "bge_embedding": "HEALTHY",
            "database": "HEALTHY",
            "llm_provider": "HEALTHY"
        },
        "initialization": {
            "server_ready": true,
            "embedding_ready": true,
            "database_ready": true
        },
        "performance": {
            "response_time_ms": 15,
            "memory_usage_mb": 245.6,
            "cpu_usage_percent": 12.5
        },
        "uptime_seconds": 3600
    }
}
```

## Error Handling

### Standard Error Response
```json
{
    "success": false,
    "error": {
        "type": "ValidationError",
        "message": "Invalid user_id format",
        "code": "INVALID_INPUT",
        "details": {
            "field": "user_id",
            "received": "invalid@user",
            "expected": "alphanumeric with hyphens"
        }
    }
}
```

### Common Error Types
- `ValidationError`: Invalid input parameters
- `AuthenticationError`: Missing or invalid user_id
- `ServiceUnavailableError`: External service (BGE, database) unavailable
- `PerformanceError`: Operation exceeded performance thresholds
- `CacheError`: Cache operation failed

## Usage Patterns

### Memory Management Workflow
1. **Add memories**: Use `add_memories` with conversation content
2. **Search relevant context**: Use `search_memory` for semantic retrieval
3. **Monitor performance**: Use `get_performance_stats` for optimization
4. **Manage conversation**: Use `get_conversation_summary` for context

### Performance Optimization Workflow
1. **Baseline measurement**: Use `get_performance_stats`
2. **Cache warming**: Use `cache_warm_common_patterns`
3. **BGE optimization**: Use `benchmark_bge_performance`
4. **Cache analysis**: Use `get_cache_performance_report`
5. **Validation**: Use `health_check` for overall system health

### Development Workflow
1. **System health**: Use `health_check` before operations
2. **BGE connectivity**: Use `get_bge_health_status`
3. **Performance monitoring**: Use `get_performance_stats`
4. **Cache management**: Use `invalidate_llm_cache` during testing

## Rate Limits and Performance

### Performance Targets (LOCOMO Benchmark)
- **P50 Latency**: < 708ms
- **P95 Latency**: < 1440ms
- **BGE Response**: < 100ms
- **Database Query**: < 50ms

### Rate Limits
- **Memory Operations**: 60 requests/minute per user
- **Search Operations**: 120 requests/minute per user
- **Performance Stats**: 10 requests/minute
- **Health Checks**: Unlimited

### Batch Operations
For bulk operations, consider:
- Use `add_memories` with longer text content (up to 8KB)
- Batch search queries in application logic
- Use `list_memories` with appropriate pagination

## Best Practices

### Memory Content
- **Atomic facts**: Store single, well-defined pieces of information
- **Context richness**: Include relevant context for better retrieval
- **User isolation**: Always specify appropriate user_id patterns

### Performance
- **Monitor regularly**: Use `get_performance_stats` for proactive monitoring
- **Cache warming**: Use `cache_warm_common_patterns` after system restart
- **BGE health**: Monitor `get_bge_health_status` for embedding service

### Error Handling
- **Graceful degradation**: Handle service unavailability gracefully
- **Retry logic**: Implement exponential backoff for transient errors
- **Performance monitoring**: Set up alerts based on performance thresholds

## Integration Examples

### Claude Code Integration
```bash
# Add development context
/spark-memory add "Working on MCP server with two-phase memory architecture"

# Search for relevant information
/spark-memory search "database configuration PostgreSQL"

# Check system health
/spark-memory health-check
```

### Direct HTTP Integration
```bash
# Health check
curl -X POST http://localhost:8050/tools/health_check \
  -H "Content-Type: application/json" \
  -d '{}'

# Add memory
curl -X POST http://localhost:8050/tools/add_memories \
  -H "Content-Type: application/json" \
  -d '{"text": "Test memory content", "user_id": "test"}'
```

---

**Version**: 1.0  
**Last Updated**: August 2024  
**Server Version**: Spark MCP v2.0 (Two-Phase Architecture)