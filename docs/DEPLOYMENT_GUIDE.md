# Spark Memory MCP Server - Deployment Guide

## Overview

This guide covers deployment options for the Spark Memory MCP Server, from development to production environments. The system is designed for a 2-person team with local infrastructure.

## Deployment Architecture

```
MCP Client ◄──► Spark Memory Server ◄──► BGE Embedding Server
    │                    │                        │
    │                    ▼                        │
    │         Memory Extraction Module            │
    │                    │                        │
    │                    ▼                        │
    │          Memory Update Module ◄─────────────┘
    │                    │
    │                    ▼
    │              Memory Database
    │             (PostgreSQL+pgvector)
    │                    │
    │                    ▼
    │           Rolling Summary Module
    │                    │
    │                    ▼
    └──────► Performance Monitor (LOCOMO)
```

## Environment Files

The project uses two environment files as per user preference:

### `.env` (Production)
```bash
# MCP Server Configuration
TRANSPORT=sse
HOST=0.0.0.0
PORT=8050

# LLM Configuration
LLM_PROVIDER=openrouter
LLM_API_KEY=your-production-api-key
LLM_CHOICE=google/gemini-2.5-flash-lite
LLM_BASE_URL=https://openrouter.ai/api/v1

# BGE Embedding Server (External)
BGE_SERVER_URL=http://************:8080

# External Supabase Database
DATABASE_URL=**************************************************************************/postgres

# Neo4j Configuration
NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=Ahayh@5096$

# User ID Configuration
MCP_USER_ID=production-user
```

### `.env.dev` (Development)
```bash
# MCP Server Configuration
TRANSPORT=sse
HOST=0.0.0.0
PORT=8050

# LLM Configuration (can use mock for testing)
LLM_PROVIDER=mock
LLM_API_KEY=mock-key
LLM_CHOICE=mock-model

# BGE Embedding Server
BGE_SERVER_URL=http://************:8080

# Development Database (local PostgreSQL)
DATABASE_URL=******************************************************/spark_memory

# Neo4j Configuration
NEO4J_URI=bolt://host.docker.internal:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=Ahayh@5096$

# Redis for caching
REDIS_URL=redis://redis:6379/0

# Development User
MCP_USER_ID=dev-team
```

## Docker Compose Files

Following user preference for only 2 Docker Compose files:

### `docker-compose.yml` (Production)
- Single service: `spark-mcp`
- Uses external services (BGE, Supabase, Neo4j)
- No mock services
- Production-ready configuration
- Health checks enabled
- Restart policies configured

### `docker-compose.dev.yml` (Development)
- Main service: `spark-mcp-dev` with hot reload
- Local PostgreSQL with pgvector
- Redis for caching
- Optional pgAdmin for database management
- Development tools and debugging support

## Deployment Options

### 1. Development Deployment

```bash
# Quick start
./setup-dev.sh
./dev.sh start

# With specific services
docker-compose -f docker-compose.dev.yml up -d

# With database management tools
docker-compose -f docker-compose.dev.yml --profile tools up -d
```

**Services included:**
- `spark-mcp-dev`: Main server with hot reload
- `postgres-dev`: Local PostgreSQL with pgvector
- `redis`: Caching layer
- `pgadmin`: Database management (profile: tools)

### 2. Production Deployment

```bash
# Basic production deployment
docker-compose up -d

# Check status
docker-compose ps
curl http://localhost:8050/tools/health_check
```

**External dependencies required:**
- BGE Embedding Server at `************:8080`
- Supabase instance at `*************:54322`
- Neo4j instance at `localhost:7687`

### 3. User-Specific Deployment

For team members with different configurations:

```bash
# User 1 (aung-dev)
cd user-specific-configs
./start-aung-dev.sh

# User 2 (yohanna)
cd user-specific-configs
./start-yohanna.sh
```

## MCP Client Configuration

### Basic Configuration
```json
{
  "spark-memory": {
    "command": "npx",
    "args": [
      "-y",
      "mcp-remote",
      "http://************:8050/sse/",
      "--allow-http"
    ],
    "env": {
      "MCP_USER_ID": "your-user-id"
    }
  }
}
```

### Multi-Context Configuration
```json
{
  "spark-memory-dev": {
    "command": "npx",
    "args": [
      "-y",
      "mcp-remote",
      "http://************:8050/sse/",
      "--allow-http"
    ],
    "env": {
      "MCP_USER_ID": "aung-dev:development"
    }
  },
  "spark-memory-prod": {
    "command": "npx", 
    "args": [
      "-y",
      "mcp-remote",
      "http://************:8051/sse/",
      "--allow-http"
    ],
    "env": {
      "MCP_USER_ID": "aung-dev:production"
    }
  }
}
```

## Health Checks & Monitoring

### Service Health
```bash
# Check MCP server
curl http://localhost:8050/tools/health_check

# Check BGE server
curl http://************:8080/health

# Check performance stats
curl http://localhost:8050/tools/get_performance_stats
```

### Docker Health Checks
```bash
# Check container health
docker-compose ps

# View health check logs
docker inspect spark-mcp-server --format='{{json .State.Health}}'
```

## Scaling & Performance

### Performance Targets (LOCOMO Benchmark)
- **P50 Latency**: < 708ms
- **P95 Latency**: < 1440ms
- **BGE Response**: < 100ms
- **Database Query**: < 50ms

### Resource Requirements
```yaml
# Recommended resource limits
deploy:
  resources:
    limits:
      memory: 2G
      cpus: '1.0'
    reservations:
      memory: 1G
      cpus: '0.5'
```

### Horizontal Scaling
For multiple users, deploy separate instances:
```bash
# User 1 on port 8050
MCP_USER_ID=user1 PORT=8050 docker-compose up -d

# User 2 on port 8051  
MCP_USER_ID=user2 PORT=8051 docker-compose up -d
```

## Security Considerations

### Production Security
1. **Change default passwords** in environment files
2. **Set proper API keys** for LLM providers
3. **Use TLS/SSL** for external connections
4. **Restrict network access** with firewall rules
5. **Enable authentication** for database access

### Environment Variables Security
```bash
# Use Docker secrets for sensitive data
echo "your_api_key" | docker secret create llm_api_key -

# Reference in compose file
secrets:
  - llm_api_key
```

## Backup & Recovery

### Database Backup
```bash
# Development database
docker-compose -f docker-compose.dev.yml exec postgres-dev pg_dump -U postgres spark_memory > backup.sql

# Restore
docker-compose -f docker-compose.dev.yml exec -T postgres-dev psql -U postgres spark_memory < backup.sql
```

### Volume Backup
```bash
# Backup persistent volumes
docker run --rm -v spark_postgres_dev_data:/data -v $(pwd):/backup alpine tar czf /backup/postgres_backup.tar.gz /data
```

## Troubleshooting

### Common Issues

**BGE Server Connection Failed**
```bash
# Check BGE server accessibility
curl http://************:8080/health

# Verify environment variable
echo $BGE_SERVER_URL
```

**Database Connection Error**
```bash
# Check PostgreSQL status
docker-compose exec postgres-dev pg_isready -U postgres

# Verify database exists
docker-compose exec postgres-dev psql -U postgres -l
```

**Permission Errors**
```bash
# Fix volume permissions
sudo chown -R $USER:$USER ./logs
sudo chown -R 999:999 ./postgres_data
```

### Reset Everything
```bash
# Stop all services
docker-compose down

# Remove volumes (WARNING: destroys data)
docker-compose down -v

# Clean rebuild
docker-compose build --no-cache
docker-compose up -d
```

## Deployment Checklist

### Pre-deployment
- [ ] Environment variables configured
- [ ] BGE server accessible
- [ ] Database connectivity verified
- [ ] Neo4j instance running
- [ ] API keys set for production
- [ ] Health checks passing

### Post-deployment
- [ ] Service health verified
- [ ] Performance targets met
- [ ] MCP client connectivity tested
- [ ] User isolation working
- [ ] Monitoring configured
- [ ] Backup procedures tested

---

**Infrastructure**: Ubuntu ************, NVIDIA RTX4060 Ti 16GB  
**External Services**: Supabase (*************:54322), BGE Server (port 8080)  
**Target Users**: 2-person development team  
**Deployment Strategy**: Docker Compose with external service dependencies
