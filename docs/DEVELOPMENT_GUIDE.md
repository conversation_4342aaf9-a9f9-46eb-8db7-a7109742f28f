# Spark Memory MCP Server - Development Guide

## Overview

This is a **Spark Memory MCP Server** implementing intelligent memory management based on Mem0's two-phase architecture. Designed for a two-person team with focus on reliability and local infrastructure.

**Key Technologies:**
- **Memory Pipeline**: Two-phase (extraction → update) based on Mem0 research
- **Embeddings**: BGE server (BAAI/bge-base-en-v1.5) with CLS pooling
- **Storage**: PostgreSQL with pgvector extension
- **Interface**: MCP (Model Context Protocol) for AI integration
- **External Services**: Supabase for production storage

## Environment Setup

### Prerequisites
- Docker and Docker Compose installed
- Access to Ubuntu server at ************
- BGE embedding server running at port 8080
- Git Bash or WSL for running shell scripts on Windows

### Critical Environment Details
- **Project Path**: `/home/<USER>/dev/tools/mcp-mem0`
- **Ubuntu Server IP**: `************`
- **BGE Server**: `http://************:8080`
- **Supabase**: External instance at `*************:54322`

### Quick Setup
```bash
# 1. Navigate to project directory
cd /home/<USER>/dev/tools/mcp-mem0

# 2. Make scripts executable
chmod +x setup-dev.sh dev.sh

# 3. Create environment configuration
cp .env.example .env.dev
# Edit .env.dev with your specific settings

# 4. Run setup
./setup-dev.sh

# 5. Start development server
./dev.sh start
```

### Environment Configuration
Critical environment variables in `.env.dev`:
```bash
# Core Settings
TRANSPORT=sse
HOST=0.0.0.0
PORT=8050

# BGE Embedding Server (IMPORTANT: Use actual IP)
BGE_SERVER_URL=http://************:8080

# Database (External Supabase instance)
DATABASE_URL=**************************************************************************/postgres

# Neo4j Configuration
NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=Ahayh@5096$

# LLM Provider
LLM_PROVIDER=openrouter
LLM_API_KEY=your-api-key-here
LLM_CHOICE=google/gemini-2.5-flash-lite

# User Context
MCP_USER_ID=dev-team
```

## Development Helper Script (./dev.sh)

### Core Commands
```bash
# Server Management
./dev.sh start          # Start development server with hot reload
./dev.sh start-d        # Start detached (background)
./dev.sh stop           # Stop all services
./dev.sh restart        # Restart services
./dev.sh logs           # View logs (follow mode)

# Development Tools
./dev.sh shell          # Interactive shell inside container
./dev.sh db-shell       # PostgreSQL shell access
./dev.sh format         # Format Python code
./dev.sh lint           # Lint Python code

# Testing
./dev.sh test           # Run all tests
./dev.sh test-unit      # Run unit tests only
./dev.sh test-integration # Run integration tests only
./dev.sh test-phase 1   # Run phase-specific tests (1-4)

# Utilities
./dev.sh clean          # Clean up Docker resources
./dev.sh build          # Rebuild development image
```

## Development Workflow

### Hot Reload Development
- Source code is mounted as a volume in `docker-compose.dev.yml`
- Changes to Python files trigger automatic reload
- No need to rebuild containers for code changes
- Monitor logs: `./dev.sh logs`

### Feature Development Cycle

#### 1. Planning Phase
- Identify requirements from documentation or user needs
- Search existing memory for similar implementations
- Plan implementation approach
- Update documentation if needed

#### 2. Implementation Phase
```bash
# Create feature branch
git checkout -b feature/new-feature

# Edit source files in src/ directory
# Changes auto-reload without container rebuild

# Test incrementally
./dev.sh test-unit
./dev.sh test-integration

# Validate performance
curl http://localhost:8050/tools/get_performance_stats
```

#### 3. Quality Assurance
```bash
# Run full test suite
./dev.sh test

# Performance validation
./dev.sh test-phase 1  # Ensure no regressions

# Code quality
./dev.sh format
./dev.sh lint
```

#### 4. Documentation & Deployment
- Update API_REFERENCE.md if tools changed
- Update README.md if architecture changed
- Test production build: `docker-compose up --build`
- Commit with proper message format

### Testing Strategy

#### Phase-Based Testing
The project uses a 4-phase testing approach:

**Phase 1: Critical Fixes**
```bash
./dev.sh test-phase 1
```
- ✅ Semantic search functionality
- ✅ P95 latency < 1 second
- ✅ Basic conflict resolution
- ✅ BGE server connectivity

**Phase 2: Memory Intelligence**
```bash
./dev.sh test-phase 2
```
- 🔄 Memory evolution and updates
- 🔄 Memory graph relationships
- 🔄 Enhanced context understanding
- 🔄 Intelligent conflict resolution

**Phase 3: User Experience**
```bash
./dev.sh test-phase 3
```
- 🔄 Advanced search capabilities
- 🔄 Memory management interface
- 🔄 Performance dashboard
- 🔄 User workflow optimization

**Phase 4: Polish & Reliability**
```bash
./dev.sh test-phase 4
```
- 🔄 Comprehensive error handling
- 🔄 Performance optimization
- 🔄 Production readiness
- 🔄 Monitoring and alerting

### Database Management

#### Development Database Access
```bash
# Access PostgreSQL shell
./dev.sh db-shell

# Common queries
SELECT COUNT(*) FROM memories;
SELECT * FROM memories WHERE user_id = 'test' LIMIT 10;
\d memories  # Show table structure

# Reset development database
docker-compose -f docker-compose.dev.yml down -v
./setup-dev.sh
```

#### pgAdmin Access
- **URL**: http://localhost:5050
- **Login**: <EMAIL> / admin
- **Server**: postgres-dev (host: postgres-dev, port: 5432)

## Debugging & Troubleshooting

### Log Analysis
```bash
# Real-time logs
./dev.sh logs

# Specific service logs
docker-compose -f docker-compose.dev.yml logs spark-mcp-dev

# Database logs
./dev.sh db-shell
```

### Interactive Debugging
```bash
# Access container shell
./dev.sh shell

# Test components individually
python -c "from bge_embedding_client import BGEEmbeddingClient; import asyncio; asyncio.run(BGEEmbeddingClient().health_check())"

# Test database connectivity
python -c "from supabase_mcp_integration import RealSupabaseMCP; import asyncio; print(asyncio.run(RealSupabaseMCP().test_connection()))"
```

### Performance Debugging
```bash
# Check BGE server
curl http://************:8080/health

# Check MCP server health
curl http://localhost:8050/tools/health_check

# Performance analysis
curl http://localhost:8050/tools/get_performance_stats | jq
```

### Common Issues

**BGE Server Connection Issues**
```bash
# Check BGE server directly
curl http://************:8080/health

# Verify environment variable
echo $BGE_SERVER_URL

# Test from container
./dev.sh shell
curl $BGE_SERVER_URL/health
```

**Database Connection Issues**
```bash
# Test database connectivity
./dev.sh db-shell

# Check environment variables
echo $DATABASE_URL

# Verify Supabase MCP access
./dev.sh shell
python -c "from supabase_mcp_integration import RealSupabaseMCP; import asyncio; print(asyncio.run(RealSupabaseMCP().test_connection()))"
```

**Hot Reload Not Working**
```bash
# Check volume mounts
docker-compose -f docker-compose.dev.yml config

# Restart development server
./dev.sh restart

# Check file permissions
ls -la src/
```

## Code Quality Standards

### Pre-commit Checklist
```bash
# Format code
./dev.sh format

# Lint code
./dev.sh lint

# Run tests
./dev.sh test

# Check performance
./dev.sh test-phase 1
```

### Python Style Guidelines
- Use type hints for all functions
- Add docstrings for all public functions
- Follow PEP 8 style guide
- Keep functions under 50 lines
- Use meaningful variable names
- Handle exceptions gracefully

### Git Commit Standards
```bash
# Commit message format
git commit -m "✨ Add feature description

- Detailed change 1
- Detailed change 2
- Performance impact or testing notes

🤖 Generated with [Claude Code](https://claude.ai/code)

Co-Authored-By: Claude <<EMAIL>>"
```

## Performance Monitoring

### Key Metrics to Monitor
```bash
# Pipeline performance (target: P95 < 1440ms)
curl http://localhost:8050/tools/get_performance_stats | jq '.performance_stats.pipeline_performance'

# BGE performance (target: < 100ms)
curl http://localhost:8050/tools/get_bge_health_status | jq '.bge_status.performance_metrics'

# Cache performance (target: > 80% hit rate)
curl http://localhost:8050/tools/get_cache_performance_report | jq '.cache_performance'
```

### Performance Optimization
```bash
# Warm cache for better performance
curl http://localhost:8050/tools/cache_warm_common_patterns

# Benchmark BGE performance
curl http://localhost:8050/tools/benchmark_bge_performance

# Clear cache for testing
curl http://localhost:8050/tools/invalidate_llm_cache
```

---

**Last Updated**: August 2024  
**Development Environment**: Docker-based with hot reload  
**Testing Strategy**: 4-phase progressive testing  
**Target Infrastructure**: NVIDIA RTX4060 Ti 16GB, Local network
