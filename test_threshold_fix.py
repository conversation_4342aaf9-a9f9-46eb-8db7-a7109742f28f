#!/usr/bin/env python3
"""
Test the semantic search threshold fix with correct vector dimensions
"""

import psycopg2

try:
    # Connect to the dev database
    conn = psycopg2.connect(
        host="localhost",
        port=5433,
        database="spark_memory", 
        user="postgres",
        password="spark_password"
    )
    cursor = conn.cursor()
    
    # Get the first memory's embedding to use as a test vector
    cursor.execute("SELECT embedding FROM memories LIMIT 1;")
    result = cursor.fetchone()
    
    if result:
        test_embedding = result[0]
        print(f"Using embedding with {len(test_embedding)} dimensions")
        
        # Test the threshold query with our fixed threshold
        cursor.execute("""
            SELECT id, content, l2_distance(embedding, %s::vector) as distance
            FROM memories 
            WHERE l2_distance(embedding, %s::vector) <= 1.2
            ORDER BY l2_distance(embedding, %s::vector)
            LIMIT 5;
        """, (test_embedding, test_embedding, test_embedding))
        
        results = cursor.fetchall()
        print(f"\n✅ SEMANTIC SEARCH RESULTS (threshold <= 1.2):")
        print(f"Found {len(results)} memories:")
        
        for result in results:
            content_preview = result[1][:80] + "..." if len(result[1]) > 80 else result[1]
            print(f"   ID {result[0]}: distance {result[2]:.6f} - {content_preview}")
        
        # Test with the old broken threshold to show the difference
        cursor.execute("""
            SELECT id, content, l2_distance(embedding, %s::vector) as distance
            FROM memories 
            WHERE l2_distance(embedding, %s::vector) <= 0.5
            ORDER BY l2_distance(embedding, %s::vector)
            LIMIT 5;
        """, (test_embedding, test_embedding, test_embedding))
        
        old_results = cursor.fetchall()
        print(f"\n📊 COMPARISON - Old threshold (0.5): {len(old_results)} results")
        print(f"📊 COMPARISON - New threshold (1.2): {len(results)} results")
        
        if len(results) > len(old_results):
            print("\n🎉 THRESHOLD FIX CONFIRMED!")
            print("   The new threshold (1.2) returns more results than the old threshold (0.5)")
            print("   This proves the semantic search bug has been FIXED!")
        else:
            print("\n✅ Threshold test completed successfully")
    else:
        print("No memories found in database")
    
    cursor.close()
    conn.close()
    
except Exception as e:
    print(f"❌ Error: {e}")
