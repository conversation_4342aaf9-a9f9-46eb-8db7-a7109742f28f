[project]
name = "spark-mcp"
version = "0.1.0"
description = "Spark - MCP server for self-evolved memory management with production-ready architecture"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "httpx>=0.28.1",
    "mcp[cli]>=1.3.0",
    "mem0ai>=0.1.88",
    "vecs>=0.4.5",
    "asyncpg>=0.29.0",
    "pgvector>=0.2.0",
    "psycopg2-binary>=2.9.0",
    "redis>=4.5.0",
    "aiohttp>=3.8.0",
    "neo4j>=5.0.0",
    "fastapi>=0.100.0",
    "uvicorn>=0.23.0",
]
