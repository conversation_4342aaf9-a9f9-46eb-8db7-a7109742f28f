"""
Enhanced Search Handler for MCP Tool

Provides backward-compatible enhanced search functionality with metadata filtering,
query expansion, and graph relationship integration.
"""

import json
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta

from enhanced_search import EnhancedMemorySearch
from exceptions import create_error_response, handle_known_exception

logger = logging.getLogger(__name__)


async def search_memory_enhanced(
    memory_system,
    query: str,
    user_id: str,
    limit: int = 5,
    filters: Optional[Dict[str, Any]] = None,
    enable_enhanced_features: bool = True
) -> Dict[str, Any]:
    """
    Enhanced search with backward compatibility.

    Args:
        memory_system: Memory system context
        query: Search query string
        user_id: User identifier
        limit: Maximum results to return
        filters: Optional enhanced filters
        enable_enhanced_features: Whether to use enhanced features

    Returns:
        Search results dictionary
    """
    # Debug logging at the start
    logger.info(f"=== SEARCH_MEMORY_ENHANCED CALLED ===")
    logger.info(f"Query: {query}")
    logger.info(f"User ID: {user_id}")
    logger.info(f"Enable enhanced features: {enable_enhanced_features}")
    logger.info(f"Has enhanced_search attr: {hasattr(memory_system, 'enhanced_search')}")

    try:
        # Force enable enhanced features and initialize if needed
        logger.info(f"🔧 FORCING ENHANCED SEARCH INITIALIZATION")
        logger.info(f"Original enable_enhanced_features: {enable_enhanced_features}")
        logger.info(f"Original has_enhanced_search: {hasattr(memory_system, 'enhanced_search')}")

        # Always try to use enhanced search - initialize if not available
        if not hasattr(memory_system, 'enhanced_search') or memory_system.enhanced_search is None:
            logger.info("🔧 Enhanced search not found, initializing now...")
            try:
                from enhanced_search import EnhancedMemorySearch
                memory_system.enhanced_search = EnhancedMemorySearch(
                    db_client=memory_system.db,
                    llm_client=getattr(memory_system, 'llm_client', None),
                    bge_client=memory_system.bge,
                    graph_service=getattr(memory_system.db, 'graph_service', None),
                    summary_manager=getattr(memory_system, 'summary', None)
                )
                logger.info("✅ Enhanced search initialized successfully")
                enable_enhanced_features = True
            except Exception as e:
                logger.error(f"❌ Failed to initialize enhanced search: {e}")
                # Fall back to basic search only if initialization fails
                enable_enhanced_features = False
        else:
            logger.info("✅ Enhanced search already available")
            enable_enhanced_features = True

        # If enhanced features are still disabled after initialization attempt, fall back to simple search
        if not enable_enhanced_features:
            # Fallback to basic vector search
            logger.info(f"Using basic vector search (enhanced features disabled after initialization attempt)")

            # Generate query embedding
            query_embedding = await memory_system.bge.embed_single(query, add_instruction=True)

            # Perform basic vector search
            from src.config import get_config
            config = get_config()
            # Use appropriate threshold for BGE embeddings with L2 distance
            threshold = 1.2  # Optimal for BGE embeddings with L2 distance (lower = more similar)
            logger.debug(f"🔍 Search threshold configuration:")
            logger.debug(f"  - Raw threshold value: {config.database.SIMILARITY_THRESHOLD}")
            logger.debug(f"  - Threshold source: {'ENV' if os.getenv('SIMILARITY_THRESHOLD') else 'DEFAULT'}")
            logger.debug(f"  - CORRECTED threshold: {threshold}")
            logger.debug(f"  - Threshold rationale: BGE embeddings with L2 distance need 1.0-1.5 range")
            
            # Log raw database results before any filtering
            logger.debug(f"🔍 Raw database results count: {len(results)}")
            if results:
                logger.debug(f"📊 First result distance: {results[0].get('distance', 'N/A'):.4f}")
                logger.debug(f"💡 First result content preview: {results[0].get('content', '')[:50]}...")
            
            # Log any post-processing that might filter results
            logger.debug(f"🔍 Starting post-processing of {len(results)} results")
            logger.debug(f"  - Current threshold: {threshold}")
            logger.debug(f"  - Distance calculation: L2 norm (embedding <-> query_embedding)")
            
            # Log filtering criteria
            logger.debug("🔍 Applying filters:")
            logger.debug(f"  - Distance threshold: < {threshold}")
            logger.debug(f"  - User ID filter: {user_id}")
            logger.debug(f"  - Session filter: {session_id if session_id else 'None'}")
            
            # Log final results after filtering
            final_results = [r for r in results if r.get('distance', float('inf')) < threshold]
            logger.debug(f"✅ Final results count after filtering: {len(final_results)}")
            if final_results:
                logger.debug(f"📈 First final result distance: {final_results[0].get('distance', 'N/A'):.4f}")
                logger.debug(f"💡 First final result content: {final_results[0].get('content', '')[:50]}...")
            else:
                logger.warning("⚠️ All results filtered out during post-processing")
                logger.debug(f"🔍 Detailed distance analysis:")
                for i, r in enumerate(results[:3]):
                    logger.debug(f"  Result {i+1}: distance={r.get('distance', 'N/A'):.4f}, content='{r.get('content', '')[:30]}...'")
            # Log query embedding stats for analysis
            if query_embedding:
                logger.debug(f"📊 Query embedding stats: min={min(query_embedding):.4f}, max={max(query_embedding):.4f}, len={len(query_embedding)}")
            
            # Log search parameters for debugging
            logger.debug(f"🔍 Performing search with parameters:")
            logger.debug(f"  - User ID: {user_id}")
            logger.debug(f"  - Query: {query}")
            logger.debug(f"  - Limit: {limit}")
            logger.debug(f"  - Enhanced features: {enable_enhanced_features}")
            if hasattr(memory_system.db, 'similarity_search_with_graph'):
                results = await memory_system.db.similarity_search_with_graph(
                    query_embedding, user_id, threshold=config.database.SIMILARITY_THRESHOLD, limit=limit
                )
            else:
                results = await memory_system.db.similarity_search(
                    query_embedding, user_id, threshold=config.database.SIMILARITY_THRESHOLD, limit=limit
                )

            # Format basic results
            return {
                "success": True,
                "query": query,
                "results": results,
                "user_id": user_id,
                "count": len(results),
                "enhanced_features_used": False
            }
        
        # Use enhanced search
        logger.info(f"Using enhanced search with features: {filters or 'default'}")
        
        # Initialize enhanced search if not already done
        if not hasattr(memory_system, 'enhanced_search'):
            memory_system.enhanced_search = EnhancedMemorySearch(
                db_client=memory_system.db,
                llm_client=memory_system.llm,
                bge_client=memory_system.bge,
                graph_service=getattr(memory_system.db, 'graph_service', None),
                summary_manager=memory_system.summary
            )
        
        # Perform enhanced search
        search_result = await memory_system.enhanced_search.search_with_filters(
            query=query,
            user_id=user_id,
            limit=limit,
            filters=filters,
            expand_query=filters.get("expand_query", True) if filters else True,
            include_graph=filters.get("include_graph", True) if filters else True
        )
        
        # Add backward compatibility fields
        search_result["count"] = search_result["metadata"]["results_returned"]
        search_result["user_id"] = user_id
        search_result["enhanced_features_used"] = True
        
        return search_result
        
    except Exception as e:
        logger.error(f"Enhanced search failed: {e}")
        # Return error in expected format
        return {
            "success": False,
            "error": str(e),
            "query": query,
            "results": [],
            "user_id": user_id,
            "count": 0,
            "enhanced_features_used": False
        }


def parse_search_filters(filter_string: Optional[str]) -> Optional[Dict[str, Any]]:
    """
    Parse filter string into structured filter dictionary.
    
    Supports formats like:
    - "last:7d" - Last 7 days
    - "context:project" - Specific context
    - "topics:python,ml" - Multiple topics
    - Combined: "last:30d,context:work,topics:api,database"
    
    Args:
        filter_string: Comma-separated filter string
        
    Returns:
        Parsed filter dictionary or None
    """
    if not filter_string:
        return None
    
    filters = {}
    
    try:
        # Split by comma and parse each filter
        parts = filter_string.split(',')
        
        for part in parts:
            part = part.strip()
            if ':' not in part:
                continue
            
            key, value = part.split(':', 1)
            key = key.strip().lower()
            value = value.strip()
            
            # Parse date range filters
            if key == "last":
                days = _parse_time_period(value)
                if days:
                    end_date = datetime.utcnow()
                    start_date = end_date - timedelta(days=days)
                    filters["date_range"] = (start_date, end_date)
            
            # Parse context filter
            elif key == "context":
                filters["context"] = value
            
            # Parse topics filter
            elif key == "topics":
                topics = [t.strip() for t in value.split(';')]
                filters["topics"] = topics
            
            # Parse boolean flags
            elif key == "recency":
                filters["recency_boost"] = value.lower() in ('true', 'yes', '1', 'on')
            
            elif key == "expand":
                filters["expand_query"] = value.lower() in ('true', 'yes', '1', 'on')
            
            elif key == "graph":
                filters["include_graph"] = value.lower() in ('true', 'yes', '1', 'on')
        
        return filters if filters else None
        
    except Exception as e:
        logger.error(f"Failed to parse search filters: {e}")
        return None


def _parse_time_period(period_str: str) -> Optional[int]:
    """
    Parse time period string to days.
    
    Supports: 1d, 7d, 1w, 30d, 1m, etc.
    
    Args:
        period_str: Time period string
        
    Returns:
        Number of days or None
    """
    try:
        period_str = period_str.lower().strip()
        
        if period_str.endswith('d'):
            return int(period_str[:-1])
        elif period_str.endswith('w'):
            return int(period_str[:-1]) * 7
        elif period_str.endswith('m'):
            return int(period_str[:-1]) * 30
        elif period_str.endswith('y'):
            return int(period_str[:-1]) * 365
        else:
            return int(period_str)  # Assume days if no unit
            
    except ValueError:
        logger.error(f"Invalid time period: {period_str}")
        return None


async def search_by_entity_handler(
    memory_system,
    entity_name: str,
    user_id: str,
    limit: int = 10
) -> Dict[str, Any]:
    """
    Search memories by entity name.
    
    Args:
        memory_system: Memory system context
        entity_name: Entity to search for
        user_id: User identifier
        limit: Maximum results
        
    Returns:
        Search results dictionary
    """
    try:
        # Check if enhanced search is available
        if not hasattr(memory_system, 'enhanced_search'):
            return {
                "success": False,
                "error": "Enhanced search not available",
                "entity": entity_name,
                "results": [],
                "user_id": user_id
            }
        
        # Perform entity search
        results = await memory_system.enhanced_search.search_by_entity(
            entity_name=entity_name,
            user_id=user_id,
            limit=limit
        )
        
        return {
            "success": True,
            "entity": entity_name,
            "results": results,
            "user_id": user_id,
            "count": len(results)
        }
        
    except Exception as e:
        logger.error(f"Entity search failed: {e}")
        return {
            "success": False,
            "error": str(e),
            "entity": entity_name,
            "results": [],
            "user_id": user_id
        }