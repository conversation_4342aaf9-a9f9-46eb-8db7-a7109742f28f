"""
Uvicorn wrapper for FastMCP to enable hot reload in development.

This wrapper allows the MCP server to work with uvicorn's --reload flag.
"""

import os
import async<PERSON>
import json
from contextlib import asynccontextmanager
from fastapi import FastAP<PERSON>, Request, Response
from fastapi.responses import StreamingResponse
import logging

# Import the MCP server
from main_new import mcp, logger

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Manage application lifecycle."""
    # Initialize MCP server
    logger.info("Starting MCP server in development mode")

    # The MCP server should already be initialized via its decorators
    # Just need to ensure it's ready

    yield

    # Cleanup
    logger.info("Shutting down MCP server")

# Create FastAPI app wrapper
app = FastAPI(title="Spark Memory MCP Server", lifespan=lifespan)

@app.get("/")
async def root():
    """Root endpoint for health checks."""
    return {"service": "spark-mcp-server", "status": "ok"}

@app.get("/health")
async def health():
    """Health check endpoint."""
    return {"status": "ok", "service": "spark-mcp-server", "mode": "development"}

@app.post("/mcp")
async def mcp_endpoint(request: Request):
    """Main MCP endpoint for SSE transport."""
    body = await request.body()

    async def event_generator():
        """Generate SSE events from MCP."""
        # This is a simplified implementation
        # In production, you'd properly handle the MCP protocol
        yield f"data: {{'type': 'message', 'content': 'MCP endpoint'}}\n\n"

    return StreamingResponse(
        event_generator(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "X-Accel-Buffering": "no",
        }
    )

@app.post("/sse/")
@app.get("/sse/")
@app.options("/sse/")
async def sse_endpoint(request: Request):
    """SSE endpoint for MCP clients (with trailing slash)."""
    if request.method == "OPTIONS":
        return Response(
            headers={
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
                "Access-Control-Allow-Headers": "Content-Type",
            }
        )
    return await mcp_sse_handler(request)

@app.post("/sse")
@app.get("/sse")
@app.options("/sse")
async def sse_endpoint_no_slash(request: Request):
    """SSE endpoint for MCP clients (without trailing slash)."""
    if request.method == "OPTIONS":
        return Response(
            headers={
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
                "Access-Control-Allow-Headers": "Content-Type",
            }
        )
    return await mcp_sse_handler(request)

async def mcp_sse_handler(request: Request):
    """Handle MCP SSE requests by proxying to the actual MCP server."""
    try:
        # Get request body for POST requests
        body = None
        if request.method == "POST":
            body = await request.body()

        async def event_generator():
            # Send initial SSE connection established message
            yield "data: {\"jsonrpc\": \"2.0\", \"method\": \"notifications/initialized\", \"params\": {}}\n\n"

            # For development, provide a basic MCP-like response
            # In a full implementation, this would proxy to the actual MCP server
            if body:
                try:
                    import json as json_lib
                    request_data = json_lib.loads(body.decode())

                    # Handle basic MCP requests
                    if request_data.get("method") == "tools/list":
                        response = {
                            "jsonrpc": "2.0",
                            "id": request_data.get("id"),
                            "result": {
                                "tools": [
                                    {"name": "add_memories", "description": "Add memories to the system"},
                                    {"name": "search_memory", "description": "Search existing memories"},
                                    {"name": "health_check", "description": "Check system health"}
                                ]
                            }
                        }
                        yield f"data: {json_lib.dumps(response)}\n\n"
                    elif request_data.get("method") == "initialize":
                        # Proper MCP initialize response
                        response = {
                            "jsonrpc": "2.0",
                            "id": request_data.get("id"),
                            "result": {
                                "protocolVersion": "2024-11-05",
                                "capabilities": {
                                    "tools": {}
                                },
                                "serverInfo": {
                                    "name": "spark-memory-dev",
                                    "version": "1.0.0-dev"
                                }
                            }
                        }
                        yield f"data: {json_lib.dumps(response)}\n\n"
                    else:
                        # Generic response for other methods
                        response = {
                            "jsonrpc": "2.0",
                            "id": request_data.get("id"),
                            "result": {"status": "development_mode", "message": "Use main_new.py for full MCP functionality"}
                        }
                        yield f"data: {json_lib.dumps(response)}\n\n"
                except Exception as parse_error:
                    logger.error(f"Error parsing MCP request: {parse_error}")
                    error_response = {
                        "jsonrpc": "2.0",
                        "error": {"code": -32700, "message": "Parse error"}
                    }
                    yield f"data: {json_lib.dumps(error_response)}\n\n"

            # Keep connection alive with periodic pings
            import asyncio
            for i in range(3):
                await asyncio.sleep(1)
                yield f"data: {{\"jsonrpc\": \"2.0\", \"method\": \"ping\", \"params\": {{\"timestamp\": {i}}}}}\n\n"

        return StreamingResponse(
            event_generator(),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "X-Accel-Buffering": "no",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
                "Access-Control-Allow-Headers": "Content-Type",
            }
        )
    except Exception as e:
        logger.error(f"SSE endpoint error: {e}")
        return {"error": f"SSE endpoint error: {str(e)}"}

# For development, list available tools
@app.get("/tools")
async def list_tools():
    """List available MCP tools for development/testing."""
    # This is a simplified list for development
    # In production, tools are accessed via MCP protocol
    return {
        "available_tools": [
            "add_memories",
            "search_memory",
            "search_by_entity",
            "get_memories",
            "delete_memory",
            "delete_all_memories",
            "health_check",
            "get_performance_stats",
            "get_bge_health_status",
            "benchmark_bge_performance"
        ],
        "note": "Use MCP client for actual tool execution"
    }

# Lifespan function moved above FastAPI app creation

if __name__ == "__main__":
    import uvicorn
    
    port = int(os.getenv("PORT", "8050"))
    uvicorn.run(
        "main_dev:app",
        host="0.0.0.0",
        port=port,
        reload=True,
        reload_dirs=["src"],
        log_level="debug" if os.getenv("ENVIRONMENT") == "development" else "info"
    )
