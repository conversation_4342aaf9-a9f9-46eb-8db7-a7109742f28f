"""
Memory Extraction Module for Two-Phase Pipeline

Processes message pairs (mt-1, mt) with conversation context to extract
salient memories for storage.
"""

import json
import logging
from typing import Tuple, List, Dict, Any, Optional
from llm_cache_service import get_llm_cache_service, CacheType
from config import get_config
from exceptions import MemoryExtractionError, LLMResponseError, LLMTimeoutError
from confidence_calculator import ConfidenceCalculator

logger = logging.getLogger(__name__)
config = get_config()

class MemoryExtractionModule:
    """
    Extraction phase of the two-phase memory pipeline.
    
    Processes message pairs with context to identify memories worth storing.
    Based on the Mem0 research architecture from arxiv:2504.19413v1.
    """
    
    def __init__(self, llm_client, bge_client):
        """
        Initialize extraction module.

        Args:
            llm_client: LLM client for memory extraction
            bge_client: BGE embedding client
        """
        self.llm_client = llm_client
        self.bge_client = bge_client
        self.cache_service = get_llm_cache_service()
        self.confidence_calculator = ConfidenceCalculator(llm_client)
        
        logger.info("Initialized MemoryExtractionModule with LLM caching and confidence scoring")
    
    async def extract_memories(
        self,
        message_pair: Tuple[str, str],
        context: Dict[str, Any]
    ) -> List[str]:
        """
        Extract salient memories from message pair with context.

        This implements the extraction phase of the two-phase pipeline.

        Args:
            message_pair: Tuple of (previous_message, current_message)
            context: Context dictionary containing:
                - conversation_summary: Current conversation summary
                - recent_messages: List of recent message contents
                - user_id: User identifier

        Returns:
            List of extracted memory strings
        """
        try:
            previous_msg, current_msg = message_pair
            
            # Build extraction prompt based on research
            prompt = self._build_extraction_prompt(
                previous_msg, current_msg, context
            )
            
            # Check cache first for performance optimization
            cache_context = {
                "user_id": context.get("user_id", "unknown"),
                "summary_length": len(context.get("conversation_summary", "")),
                "recent_count": len(context.get("recent_messages", []))
            }
            
            cached_response = await self.cache_service.get_cached_response(
                CacheType.EXTRACTION, prompt, cache_context
            )
            
            if cached_response:
                logger.debug("Using cached extraction response")
                response = cached_response
            else:
                # Generate extraction using LLM
                logger.debug("Generating new extraction response")
                response = await self.llm_client.generate(prompt)
                
                # Cache the response for future use
                if response and response.strip():
                    await self.cache_service.cache_response(
                        CacheType.EXTRACTION, prompt, response, cache_context
                    )
            
            # Parse response with enhanced fallback for Gemini models
            memories = self._parse_llm_response(response)
            
            # Filter and clean memories
            cleaned_memories = self._filter_memories(memories)
            
            logger.debug(f"Extracted {len(cleaned_memories)} memories from message pair")
            return cleaned_memories
            
        except (MemoryExtractionError, LLMResponseError, LLMTimeoutError) as e:
            logger.error(f"Memory extraction failed: {e}")
            return []
        except Exception as e:
            logger.error(f"Unexpected error in memory extraction: {e}")
            raise MemoryExtractionError(
                f"Memory extraction failed due to unexpected error: {str(e)}",
                llm_response=response if 'response' in locals() else None
            )

    async def extract_memories_with_confidence(
        self,
        message_pair: Tuple[str, str],
        context: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """
        Extract memories with confidence scores.

        This is the enhanced version that includes confidence calculation
        for Phase 2 memory quality assessment.

        Args:
            message_pair: Tuple of (previous_message, current_message)
            context: Context dictionary

        Returns:
            List of memory dictionaries with confidence scores
        """
        try:
            # First extract memories using existing method
            memory_strings = await self.extract_memories(message_pair, context)

            if not memory_strings:
                return []

            # Calculate confidence for each memory
            memories_with_confidence = []
            extraction_context = f"Previous: {message_pair[0][:100]}... Current: {message_pair[1][:100]}..."

            for memory_content in memory_strings:
                try:
                    # Calculate confidence score
                    confidence_score, confidence_factors = await self.confidence_calculator.calculate_initial_confidence(
                        memory_content=memory_content,
                        extraction_context=extraction_context,
                        source_type="user_input"
                    )

                    # Create memory object with confidence
                    memory_obj = {
                        'content': memory_content,
                        'confidence_score': confidence_score,
                        'confidence_factors': self.confidence_calculator.to_dict(confidence_factors),
                        'extraction_context': extraction_context
                    }

                    memories_with_confidence.append(memory_obj)

                    logger.debug(f"Memory confidence {confidence_score:.3f}: {memory_content[:50]}...")

                except Exception as e:
                    logger.warning(f"Failed to calculate confidence for memory '{memory_content[:50]}...': {e}")
                    # Add memory with default confidence
                    memory_obj = {
                        'content': memory_content,
                        'confidence_score': 0.5,
                        'confidence_factors': {'error': str(e), 'default_assigned': True},
                        'extraction_context': extraction_context
                    }
                    memories_with_confidence.append(memory_obj)

            logger.info(f"Extracted {len(memories_with_confidence)} memories with confidence scores")
            return memories_with_confidence

        except Exception as e:
            logger.error(f"Error in extract_memories_with_confidence: {e}")
            return []

    def _build_extraction_prompt(
        self, 
        previous_msg: str, 
        current_msg: str, 
        context: Dict[str, Any]
    ) -> str:
        """
        Build the enhanced extraction prompt focusing on evolution and relationships.
        Based on Mem0 research patterns with enhanced intelligence for memory inference.
        
        Args:
            previous_msg: Previous message in the conversation
            current_msg: Current message in the conversation
            context: Conversation context
            
        Returns:
            Formatted extraction prompt
        """
        conversation_summary = context.get('conversation_summary', 'None')
        recent_messages = context.get('recent_messages', [])
        user_id = context.get('user_id', 'unknown')
        similar_memories = context.get('similar_memories', [])
        
        # Build recent context
        recent_context = "\\n".join(recent_messages[-5:]) if recent_messages else "None"
        
        # Build similar memories context if available
        similar_context = ""
        if similar_memories:
            similar_context = f"""
Similar Existing Memories:
{chr(10).join(similar_memories[:3])}
"""
        
        # Enhanced extraction prompt with focus on evolution and relationships
        prompt = f"""Extract key facts from the conversation, focusing on evolution, relationships, and temporal context.

FOCUS ON INTELLIGENCE EXTRACTION:
1. User preferences that may change over time (note evolution: "now prefers X, previously Y")
2. Current projects and their status updates (track progression)
3. Relationships between people, projects, concepts, and technologies
4. Temporal context - when things happened, will happen, or changed
5. Updates or corrections to previous information
6. Knowledge evolution - new learnings, changed opinions, skill development

RELATIONSHIP EXTRACTION:
- Person-to-person relationships (colleagues, mentors, team members)
- Person-to-project relationships (ownership, contribution, involvement)
- Project-to-technology relationships (uses, depends on, replaces)
- Concept-to-concept relationships (causes, enables, conflicts with)

INPUT:
Previous Message: {previous_msg}
Current Message: {current_msg}
Conversation Summary: {conversation_summary}
Recent Context: {recent_context}{similar_context}

ENHANCED EXTRACTION RULES:
- Extract only factual, actionable information valuable for future conversations
- Show evolution: "User now prefers X (previously preferred Y)"
- Show relationships: "Project Y uses technology Z", "User collaborates with Person A"
- Include temporal markers: "As of [date]", "Currently", "Recently changed to"
- Skip temporary states, generic responses, or redundant information
- Each memory must be a complete, standalone sentence with context
- Maximum 5 memories per extraction

FORMAT EXAMPLES FOR EVOLUTION:
["User now prefers TypeScript over JavaScript for new projects (changed from previous JavaScript preference)"]
["Project status changed from development to testing phase as of this week"]
["User learned GraphQL and now considers it better than REST for their use case"]

FORMAT EXAMPLES FOR RELATIONSHIPS:
["User collaborates with Sarah on the mobile app project using React Native"]
["Customer analytics project depends on PostgreSQL database and Python data pipeline"]
["User's team lead is Michael who provides guidance on architecture decisions"]

CRITICAL: You MUST respond with ONLY a valid JSON array of strings. No other text, explanations, or formatting.

Response (JSON array only):"""
        
        return prompt
    
    def _parse_llm_response(self, response: str) -> List[str]:
        """
        Enhanced LLM response parsing with robust fallback for Gemini models.
        
        Args:
            response: Raw LLM response string
            
        Returns:
            List of extracted memory strings
        """
        if not response or not response.strip():
            logger.warning("Empty LLM response")
            return []
        
        response = response.strip()
        
        # Method 1: Try direct JSON parsing
        try:
            memories = json.loads(response)
            if isinstance(memories, list):
                logger.debug("Successfully parsed JSON list")
                return [str(m) for m in memories if m and str(m).strip()]
            elif isinstance(memories, str):
                logger.debug("Parsed JSON string, converting to list")
                return [memories] if memories.strip() else []
            else:
                logger.warning(f"JSON parsed but unexpected type: {type(memories)}")
                return [str(memories)] if str(memories).strip() else []
        except json.JSONDecodeError:
            pass
        
        # Method 2: Extract JSON array from response with surrounding text
        import re
        json_match = re.search(r'\[.*?\]', response, re.DOTALL)
        if json_match:
            try:
                memories = json.loads(json_match.group())
                if isinstance(memories, list):
                    logger.debug("Extracted JSON array from response text")
                    return [str(m) for m in memories if m and str(m).strip()]
            except json.JSONDecodeError:
                pass
        
        # Method 3: Parse lines that look like list items
        lines = response.split('\n')
        memories = []
        for line in lines:
            line = line.strip()
            # Remove common list prefixes and quotes
            line = re.sub(r'^[-*•]\s*', '', line)  # Remove bullet points
            line = re.sub(r'^"\s*|"\s*,?\s*$', '', line)  # Remove quotes
            line = re.sub(r'^\d+\.\s*', '', line)  # Remove numbered lists
            
            if line and len(line) > config.memory.MIN_MEMORY_LENGTH and not line.lower().startswith(('here', 'the', 'response')):
                memories.append(line)
        
        if memories:
            logger.debug(f"Extracted {len(memories)} memories from line parsing")
            return memories
        
        # Method 4: Treat entire response as single memory if it looks meaningful
        if len(response) > config.memory.MIN_MEANINGFUL_CONTENT_LENGTH and not any(word in response.lower() for word in ['sorry', 'cannot', 'unable', 'error']):
            # Clean up the response
            cleaned = re.sub(r'^["\[\{]|["\]\}]$', '', response)  # Remove surrounding brackets/quotes
            cleaned = cleaned.strip()
            if cleaned:
                logger.debug("Treating cleaned response as single memory")
                return [cleaned]
        
        logger.warning(f"Failed to parse LLM response with all methods: {response[:config.memory.SUMMARY_PREVIEW_LENGTH]}...")
        return []
    
    def _filter_memories(self, memories: List[str]) -> List[str]:
        """
        Filter and clean extracted memories.
        
        Args:
            memories: Raw extracted memories
            
        Returns:
            Filtered and cleaned memories
        """
        cleaned = []
        
        for memory in memories:
            if not isinstance(memory, str):
                memory = str(memory)
            
            # Clean and validate memory
            memory = memory.strip()
            
            # Skip empty or too short memories
            if len(memory) < config.memory.MIN_MEMORY_LENGTH:
                continue
            
            # Skip generic responses
            if any(pattern in memory.lower() for pattern in config.memory.GENERIC_RESPONSE_PATTERNS):
                continue
            
            # Skip if memory is too long (likely contains conversation flow)
            if len(memory) > config.memory.MAX_MEMORY_LENGTH:
                continue
            
            cleaned.append(memory)
        
        return cleaned
    
    async def extract_from_single_message(
        self, 
        message: str, 
        context: Dict[str, Any]
    ) -> List[str]:
        """
        Extract memories from a single message (fallback for when no previous message).
        
        Args:
            message: Single message to extract from
            context: Conversation context
            
        Returns:
            List of extracted memory strings
        """
        # Create a message pair with empty previous message
        message_pair = ("", message)
        return await self.extract_memories(message_pair, context)
    
    async def extract_with_embedding_context(
        self, 
        message_pair: Tuple[str, str], 
        context: Dict[str, Any],
        similar_memories: List[Dict[str, Any]] = None
    ) -> List[str]:
        """
        Extract memories with additional context from similar existing memories.
        
        Args:
            message_pair: Tuple of (previous_message, current_message)
            context: Conversation context
            similar_memories: List of similar existing memories for context
            
        Returns:
            List of extracted memory strings
        """
        if similar_memories:
            # Add similar memories to context for extraction
            similar_content = [mem['content'] for mem in similar_memories[:3]]
            context = context.copy()
            context['similar_memories'] = similar_content
        
        return await self.extract_memories(message_pair, context)
    
    async def extract_with_enhanced_metadata(
        self, 
        message_pair: Tuple[str, str], 
        context: Dict[str, Any],
        enhanced_metadata: Dict[str, Any] = None
    ) -> Tuple[List[str], Dict[str, Any]]:
        """
        Extract memories with enhanced metadata for relationship tracking and evolution.
        
        Args:
            message_pair: Tuple of (previous_message, current_message)
            context: Conversation context
            enhanced_metadata: Additional metadata for memory intelligence
            
        Returns:
            Tuple of (extracted memories, enhanced metadata dict)
        """
        import uuid
        from datetime import datetime
        
        # Extract memories first
        memories = await self.extract_memories(message_pair, context)
        
        # Build enhanced metadata
        metadata = {
            "conversation_id": enhanced_metadata.get("conversation_id", str(uuid.uuid4())) if enhanced_metadata else str(uuid.uuid4()),
            "timestamp": datetime.utcnow().isoformat(),
            "context_type": enhanced_metadata.get("context", "general") if enhanced_metadata else "general",
            "session_phase": enhanced_metadata.get("session_phase", "active") if enhanced_metadata else "active",
            "source": "enhanced_mcp_server",
            "version": "1.1",
            "extraction_features": {
                "evolution_detected": self._detect_evolution_in_memories(memories),
                "relationships_detected": self._detect_relationships_in_memories(memories),
                "temporal_context": self._extract_temporal_markers(memories),
                "entities_mentioned": self._extract_entities_from_memories(memories)
            },
            "conversation_context": {
                "has_previous_msg": bool(message_pair[0].strip()),
                "message_length": len(message_pair[1]),
                "summary_length": len(context.get('conversation_summary', '')),
                "recent_context_size": len(context.get('recent_messages', []))
            }
        }
        
        return memories, metadata
    
    def _detect_evolution_in_memories(self, memories: List[str]) -> bool:
        """Detect if memories contain evolution patterns."""
        evolution_indicators = [
            'now prefers', 'previously', 'changed from', 'updated to', 
            'learned', 'discovered', 'realized', 'switched to', 
            'no longer', 'instead of', 'evolved to'
        ]
        return any(
            any(indicator in memory.lower() for indicator in evolution_indicators)
            for memory in memories
        )
    
    def _detect_relationships_in_memories(self, memories: List[str]) -> bool:
        """Detect if memories contain relationship patterns."""
        relationship_indicators = [
            'collaborates with', 'works with', 'team', 'uses', 'depends on',
            'relates to', 'connected to', 'part of', 'member of', 'leads',
            'reports to', 'manages', 'contributes to', 'involved in'
        ]
        return any(
            any(indicator in memory.lower() for indicator in relationship_indicators)
            for memory in memories
        )
    
    def _extract_temporal_markers(self, memories: List[str]) -> List[str]:
        """Extract temporal context from memories."""
        import re
        temporal_patterns = [
            r'as of \w+', r'currently', r'recently', r'now', 
            r'this week', r'this month', r'today', r'yesterday',
            r'\d{4}-\d{2}-\d{2}', r'in \d+ days?', r'last \w+'
        ]
        
        temporal_markers = []
        for memory in memories:
            for pattern in temporal_patterns:
                matches = re.findall(pattern, memory.lower())
                temporal_markers.extend(matches)
        
        return list(set(temporal_markers))
    
    def _extract_entities_from_memories(self, memories: List[str]) -> Dict[str, List[str]]:
        """Extract entities (people, projects, technologies) from memories."""
        import re
        
        entities = {
            "people": [],
            "projects": [],
            "technologies": []
        }
        
        # Simple patterns for entity extraction (could be enhanced with NER)
        people_patterns = [r'collaborates with (\w+)', r'works with (\w+)', r'team lead is (\w+)']
        project_patterns = [r'(\w+\s+project)', r'project (\w+)', r'working on (\w+)']
        tech_patterns = [r'using (\w+)', r'prefers (\w+)', r'learned (\w+)', r'technology (\w+)']
        
        for memory in memories:
            # Extract people
            for pattern in people_patterns:
                matches = re.findall(pattern, memory, re.IGNORECASE)
                entities["people"].extend(matches)
            
            # Extract projects
            for pattern in project_patterns:
                matches = re.findall(pattern, memory, re.IGNORECASE)
                entities["projects"].extend(matches)
            
            # Extract technologies
            for pattern in tech_patterns:
                matches = re.findall(pattern, memory, re.IGNORECASE)
                entities["technologies"].extend(matches)
        
        # Remove duplicates and clean up
        for entity_type in entities:
            entities[entity_type] = list(set([e.strip() for e in entities[entity_type] if e.strip()]))
        
        return entities