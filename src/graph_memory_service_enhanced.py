"""
Enhanced Graph Memory Service with improved user isolation.

Adds user prefixing and namespace isolation for better multi-user support.
"""

import json
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime

from graph_memory_service import GraphMemoryService

logger = logging.getLogger(__name__)

class EnhancedGraphMemoryService(GraphMemoryService):
    """
    Enhanced version with better user isolation for multi-user environments.
    """
    
    def _get_user_prefix(self, user_id: str) -> str:
        """Get user-specific prefix for entity names."""
        # Convert user_id to safe prefix
        safe_user = user_id.replace("@", "_").replace(".", "_").replace("-", "_")
        return f"user_{safe_user}"
    
    def _get_prefixed_entity_name(self, entity_name: str, user_id: str) -> str:
        """Get entity name with user prefix."""
        prefix = self._get_user_prefix(user_id)
        return f"{prefix}::{entity_name}"
    
    def _get_user_namespace_label(self, user_id: str) -> str:
        """Get user-specific label for entities."""
        prefix = self._get_user_prefix(user_id)
        return f"User_{prefix}"
    
    async def create_entities_and_relationships(self, entities: List[Dict], memory_id: str, user_id: str):
        """
        Create entities with user namespace isolation.
        """
        if not entities:
            return
        
        try:
            user_label = self._get_user_namespace_label(user_id)
            
            def _create_entities_tx(tx, entities_batch, memory_id, user_id):
                # Create memory node with user namespace
                tx.run(f"""
                    MERGE (m:Memory:{user_label} {{id: $memory_id}})
                    SET m.user_id = $user_id, m.created_at = datetime(), m.updated_at = datetime()
                """, memory_id=memory_id, user_id=user_id)
                
                # Create entities with prefixed names and user labels
                for entity in entities_batch:
                    prefixed_name = self._get_prefixed_entity_name(entity["name"], user_id)
                    
                    tx.run(f"""
                        MERGE (e:Entity:{user_label} {{name: $prefixed_name}})
                        SET e.original_name = $original_name,
                            e.type = $type, 
                            e.properties = $properties,
                            e.user_id = $user_id,
                            e.updated_at = datetime()
                        ON CREATE SET e.created_at = datetime()
                        WITH e
                        MATCH (m:Memory:{user_label} {{id: $memory_id}})
                        MERGE (e)-[:MENTIONED_IN]->(m)
                    """, 
                    prefixed_name=prefixed_name,
                    original_name=entity["name"],
                    type=entity["type"], 
                    properties=entity.get("properties", {}),
                    user_id=user_id,
                    memory_id=memory_id)
            
            # Process entities in batches
            batch_size = 10
            with self.driver.session() as session:
                for i in range(0, len(entities), batch_size):
                    batch = entities[i:i + batch_size]
                    session.execute_write(_create_entities_tx, batch, memory_id, user_id)
            
            logger.info(f"Created {len(entities)} namespaced entities for user {user_id}")
            
        except Exception as e:
            logger.error(f"Failed to create namespaced entities: {e}")
            raise
    
    async def find_related_memories(self, memory_id: str, user_id: str, depth: int = 2) -> List[str]:
        """
        Find related memories within user namespace only.
        """
        user_label = self._get_user_namespace_label(user_id)
        
        def _find_related_tx(tx, memory_id, user_id, depth):
            query = f"""
            MATCH (m:Memory:{user_label} {{id: $memory_id, user_id: $user_id}})<-[:MENTIONED_IN]-(e:Entity:{user_label})
            MATCH (e)-[*1..{depth}]-(other_entity:Entity:{user_label})-[:MENTIONED_IN]->(related:Memory:{user_label} {{user_id: $user_id}})
            WHERE related.id <> $memory_id
            RETURN DISTINCT related.id as related_id, 
                   COUNT(*) as connection_strength
            ORDER BY connection_strength DESC
            LIMIT 10
            """
            
            result = tx.run(query, memory_id=memory_id, user_id=user_id)
            return [(record["related_id"], record["connection_strength"]) for record in result]
        
        try:
            with self.driver.session() as session:
                results = session.execute_read(_find_related_tx, memory_id, user_id, min(depth, 3))
                return [result[0] for result in results]
                
        except Exception as e:
            logger.error(f"Failed to find related memories: {e}")
            return []
    
    async def get_entity_relationships(self, entity_name: str, user_id: str) -> Dict[str, Any]:
        """
        Get entity relationships within user namespace.
        """
        user_label = self._get_user_namespace_label(user_id) 
        prefixed_name = self._get_prefixed_entity_name(entity_name, user_id)
        
        def _get_relationships_tx(tx, prefixed_name, user_id):
            query = f"""
            MATCH (e:Entity:{user_label} {{name: $prefixed_name, user_id: $user_id}})
            OPTIONAL MATCH (e)-[r]->(other:Entity:{user_label})
            OPTIONAL MATCH (incoming:Entity:{user_label})-[r_in]->(e)
            RETURN e,
                   COLLECT(DISTINCT {{target: other.original_name, type: type(r), properties: r.properties}}) as outgoing,
                   COLLECT(DISTINCT {{source: incoming.original_name, type: type(r_in), properties: r_in.properties}}) as incoming
            """
            
            result = tx.run(query, prefixed_name=prefixed_name, user_id=user_id)
            record = result.single()
            
            if not record:
                return None
                
            entity = record["e"]
            return {
                "entity": {
                    "name": entity.get("original_name", entity["name"]),
                    "type": entity.get("type"),
                    "properties": entity.get("properties", {})
                },
                "outgoing_relationships": record["outgoing"],
                "incoming_relationships": record["incoming"]
            }
        
        try:
            with self.driver.session() as session:
                return session.execute_read(_get_relationships_tx, prefixed_name, user_id)
                
        except Exception as e:
            logger.error(f"Failed to get entity relationships: {e}")
            return None