"""
Session Management Module for Spark Memory MCP

Provides session lifecycle management with automatic expiration,
session-based memory retrieval, and conversation context tracking.
"""

import asyncio
import json
import logging
import uuid
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta, timezone
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class Session:
    """Session data structure."""
    id: str
    user_id: str
    session_name: Optional[str]
    status: str
    metadata: Dict[str, Any]
    created_at: datetime
    updated_at: datetime
    expires_at: Optional[datetime]
    ended_at: Optional[datetime]
    
    def is_active(self) -> bool:
        """Check if session is active and not expired."""
        if self.status != 'active':
            return False
        if self.expires_at:
            # Ensure both datetimes are timezone-naive for comparison
            now = datetime.now()
            expires_at = self.expires_at

            # If expires_at is timezone-aware, convert to naive
            if expires_at.tzinfo is not None:
                expires_at = expires_at.replace(tzinfo=None)

            if now > expires_at:
                return False
        return True
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert session to dictionary."""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'session_name': self.session_name,
            'status': self.status,
            'metadata': self.metadata,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'expires_at': self.expires_at.isoformat() if self.expires_at else None,
            'ended_at': self.ended_at.isoformat() if self.ended_at else None,
            'is_active': self.is_active()
        }

class SessionManager:
    """
    Session lifecycle management for conversation and memory tracking.
    
    Provides session creation, expiration, and context management
    for enhanced memory operations.
    """
    
    def __init__(self, db_service):
        """
        Initialize session manager.
        
        Args:
            db_service: Enhanced database service instance
        """
        self.db_service = db_service
        self.default_session_hours = 24
        self.cleanup_interval = 3600  # 1 hour in seconds
        self._cleanup_task = None
        
    async def start_cleanup_task(self):
        """Start background task for session cleanup."""
        if self._cleanup_task is None:
            self._cleanup_task = asyncio.create_task(self._periodic_cleanup())
            logger.info("Session cleanup task started")
    
    async def stop_cleanup_task(self):
        """Stop background cleanup task."""
        if self._cleanup_task:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
            self._cleanup_task = None
            logger.info("Session cleanup task stopped")
    
    async def _periodic_cleanup(self):
        """Periodic cleanup of expired sessions."""
        while True:
            try:
                await asyncio.sleep(self.cleanup_interval)
                expired_count = await self.expire_old_sessions()
                if expired_count > 0:
                    logger.info(f"Expired {expired_count} old sessions")
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in session cleanup: {e}")
    
    async def create_session(
        self,
        user_id: str,
        session_name: Optional[str] = None,
        expires_in_hours: int = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Session:
        """
        Create a new session.
        
        Args:
            user_id: User identifier
            session_name: Optional human-readable session name
            expires_in_hours: Hours until expiration (default: 24)
            metadata: Additional session metadata
            
        Returns:
            Created session object
        """
        if expires_in_hours is None:
            expires_in_hours = self.default_session_hours
        
        if metadata is None:
            metadata = {}
        
        # Use database function to create session
        sql = "SELECT create_session($1, $2, $3, $4)"
        result = await self.db_service.execute_sql(
            sql, 
            [user_id, session_name, expires_in_hours, json.dumps(metadata)]
        )
        
        session_id = result[0]['create_session']
        
        # Retrieve the created session
        return await self.get_session(session_id)
    
    async def get_session(self, session_id: str) -> Optional[Session]:
        """
        Get session by ID.
        
        Args:
            session_id: Session identifier
            
        Returns:
            Session object or None if not found
        """
        sql = """
            SELECT id, user_id, session_name, status, metadata,
                   created_at, updated_at, expires_at, ended_at
            FROM sessions 
            WHERE id = $1
        """
        
        result = await self.db_service.execute_sql(sql, [session_id])
        
        if not result:
            return None
        
        row = result[0]
        return Session(
            id=str(row['id']),
            user_id=row['user_id'],
            session_name=row['session_name'],
            status=row['status'],
            metadata=row['metadata'] or {},
            created_at=row['created_at'],
            updated_at=row['updated_at'],
            expires_at=row['expires_at'],
            ended_at=row['ended_at']
        )
    
    async def get_user_sessions(
        self, 
        user_id: str, 
        status: Optional[str] = None,
        limit: int = 10
    ) -> List[Session]:
        """
        Get sessions for a user.
        
        Args:
            user_id: User identifier
            status: Optional status filter ('active', 'ended', etc.)
            limit: Maximum number of sessions to return
            
        Returns:
            List of session objects
        """
        if status:
            sql = """
                SELECT id, user_id, session_name, status, metadata,
                       created_at, updated_at, expires_at, ended_at
                FROM sessions 
                WHERE user_id = $1 AND status = $2
                ORDER BY updated_at DESC
                LIMIT $3
            """
            params = [user_id, status, limit]
        else:
            sql = """
                SELECT id, user_id, session_name, status, metadata,
                       created_at, updated_at, expires_at, ended_at
                FROM sessions 
                WHERE user_id = $1
                ORDER BY updated_at DESC
                LIMIT $2
            """
            params = [user_id, limit]
        
        result = await self.db_service.execute_sql(sql, params)
        
        sessions = []
        for row in result:
            sessions.append(Session(
                id=str(row['id']),
                user_id=row['user_id'],
                session_name=row['session_name'],
                status=row['status'],
                metadata=row['metadata'] or {},
                created_at=row['created_at'],
                updated_at=row['updated_at'],
                expires_at=row['expires_at'],
                ended_at=row['ended_at']
            ))
        
        return sessions
    
    async def get_active_session(self, user_id: str) -> Optional[Session]:
        """
        Get the most recent active session for a user.
        
        Args:
            user_id: User identifier
            
        Returns:
            Most recent active session or None
        """
        sessions = await self.get_user_sessions(user_id, status='active', limit=1)
        return sessions[0] if sessions else None
    
    async def end_session(self, session_id: str) -> bool:
        """
        End a session.
        
        Args:
            session_id: Session identifier
            
        Returns:
            True if session was ended, False if not found or already ended
        """
        sql = "SELECT end_session($1)"
        result = await self.db_service.execute_sql(sql, [session_id])
        return result[0]['end_session'] if result else False
    
    async def update_session_metadata(
        self, 
        session_id: str, 
        metadata: Dict[str, Any]
    ) -> bool:
        """
        Update session metadata.
        
        Args:
            session_id: Session identifier
            metadata: New metadata to merge
            
        Returns:
            True if updated successfully
        """
        sql = """
            UPDATE sessions 
            SET metadata = metadata || $2::jsonb,
                updated_at = NOW()
            WHERE id = $1 AND status = 'active'
        """
        
        result = await self.db_service.execute_sql(
            sql, 
            [session_id, json.dumps(metadata)]
        )
        return True  # asyncpg doesn't return rowcount easily
    
    async def expire_old_sessions(self) -> int:
        """
        Expire old sessions using database function.
        
        Returns:
            Number of sessions expired
        """
        sql = "SELECT expire_old_sessions()"
        result = await self.db_service.execute_sql(sql)
        return result[0]['expire_old_sessions'] if result else 0
    
    async def get_session_stats(self, session_id: str) -> Dict[str, Any]:
        """
        Get statistics for a session.
        
        Args:
            session_id: Session identifier
            
        Returns:
            Dictionary with session statistics
        """
        sql = """
            SELECT 
                COUNT(m.id) as memory_count,
                MIN(m.created_at) as first_memory_at,
                MAX(m.created_at) as last_memory_at,
                COUNT(DISTINCT DATE(m.created_at)) as active_days,
                COUNT(cs.user_id) as summary_count
            FROM sessions s
            LEFT JOIN memories m ON s.id = m.session_id
            LEFT JOIN conversation_summaries cs ON s.id = cs.session_id
            WHERE s.id = $1
            GROUP BY s.id
        """
        
        result = await self.db_service.execute_sql(sql, [session_id])
        
        if not result:
            return {
                'memory_count': 0,
                'summary_count': 0,
                'active_days': 0,
                'first_memory_at': None,
                'last_memory_at': None
            }
        
        row = result[0]
        return {
            'memory_count': row['memory_count'],
            'summary_count': row['summary_count'],
            'active_days': row['active_days'],
            'first_memory_at': row['first_memory_at'].isoformat() if row['first_memory_at'] else None,
            'last_memory_at': row['last_memory_at'].isoformat() if row['last_memory_at'] else None
        }
