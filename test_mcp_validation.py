#!/usr/bin/env python3
"""
MCP Tools Validation Script
Tests all 12 MCP tools for the Spark Memory MCP Server
"""

import asyncio
import json
import sys
import aiohttp
from typing import Dict, Any, List

class MCPClient:
    def __init__(self, base_url: str = "http://localhost:8050"):
        self.base_url = base_url
        self.session = None
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def call_tool(self, tool_name: str, arguments: Dict[str, Any] = None) -> Dict[str, Any]:
        """Call an MCP tool via HTTP"""
        if arguments is None:
            arguments = {}
        
        url = f"{self.base_url}/tools/{tool_name}"
        
        try:
            async with self.session.post(url, json=arguments) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    return {
                        "error": f"HTTP {response.status}",
                        "message": await response.text()
                    }
        except Exception as e:
            return {
                "error": "Connection failed",
                "message": str(e)
            }

async def test_all_mcp_tools():
    """Test all 12 MCP tools"""
    
    print("🧪 Starting Comprehensive MCP Tools Validation")
    print("=" * 60)
    
    async with MCPClient() as client:
        
        # Test 1: Health Check
        print("\n1. Testing health_check...")
        result = await client.call_tool("health_check")
        if "error" not in result:
            print("✅ health_check: PASSED")
        else:
            print(f"❌ health_check: FAILED - {result}")
        
        # Test 2: Performance Stats
        print("\n2. Testing get_performance_stats...")
        result = await client.call_tool("get_performance_stats")
        if "error" not in result:
            print("✅ get_performance_stats: PASSED")
        else:
            print(f"❌ get_performance_stats: FAILED - {result}")
        
        # Test 3: BGE Health Status
        print("\n3. Testing get_bge_health_status...")
        result = await client.call_tool("get_bge_health_status")
        if "error" not in result:
            print("✅ get_bge_health_status: PASSED")
        else:
            print(f"❌ get_bge_health_status: FAILED - {result}")
        
        # Test 4: Add Memory
        print("\n4. Testing add_memory...")
        result = await client.call_tool("add_memory", {
            "content": "This is a test memory for validation",
            "metadata": {"test": True, "validation": "mcp_tools"}
        })
        if "error" not in result:
            print("✅ add_memory: PASSED")
            memory_id = result.get("memory_id")
        else:
            print(f"❌ add_memory: FAILED - {result}")
            memory_id = None
        
        # Test 5: Search Memories
        print("\n5. Testing search_memories...")
        result = await client.call_tool("search_memories", {
            "query": "test memory validation",
            "limit": 5
        })
        if "error" not in result:
            print("✅ search_memories: PASSED")
            print(f"   Found {len(result.get('memories', []))} memories")
        else:
            print(f"❌ search_memories: FAILED - {result}")
        
        # Test 6: Get Memory (if we have a memory_id)
        if memory_id:
            print("\n6. Testing get_memory...")
            result = await client.call_tool("get_memory", {"memory_id": memory_id})
            if "error" not in result:
                print("✅ get_memory: PASSED")
            else:
                print(f"❌ get_memory: FAILED - {result}")
        else:
            print("\n6. Skipping get_memory (no memory_id available)")
        
        # Test 7: Update Memory (if we have a memory_id)
        if memory_id:
            print("\n7. Testing update_memory...")
            result = await client.call_tool("update_memory", {
                "memory_id": memory_id,
                "content": "Updated test memory for validation",
                "metadata": {"test": True, "validation": "mcp_tools", "updated": True}
            })
            if "error" not in result:
                print("✅ update_memory: PASSED")
            else:
                print(f"❌ update_memory: FAILED - {result}")
        else:
            print("\n7. Skipping update_memory (no memory_id available)")
        
        # Test 8: Cache Performance Report
        print("\n8. Testing get_cache_performance_report...")
        result = await client.call_tool("get_cache_performance_report")
        if "error" not in result:
            print("✅ get_cache_performance_report: PASSED")
        else:
            print(f"❌ get_cache_performance_report: FAILED - {result}")
        
        # Test 9: Benchmark BGE Performance
        print("\n9. Testing benchmark_bge_performance...")
        result = await client.call_tool("benchmark_bge_performance")
        if "error" not in result:
            print("✅ benchmark_bge_performance: PASSED")
        else:
            print(f"❌ benchmark_bge_performance: FAILED - {result}")
        
        # Test 10: Cache Warm Common Patterns
        print("\n10. Testing cache_warm_common_patterns...")
        result = await client.call_tool("cache_warm_common_patterns")
        if "error" not in result:
            print("✅ cache_warm_common_patterns: PASSED")
        else:
            print(f"❌ cache_warm_common_patterns: FAILED - {result}")
        
        # Test 11: Invalidate LLM Cache
        print("\n11. Testing invalidate_llm_cache...")
        result = await client.call_tool("invalidate_llm_cache")
        if "error" not in result:
            print("✅ invalidate_llm_cache: PASSED")
        else:
            print(f"❌ invalidate_llm_cache: FAILED - {result}")
        
        # Test 12: Delete Memory (if we have a memory_id)
        if memory_id:
            print("\n12. Testing delete_memory...")
            result = await client.call_tool("delete_memory", {"memory_id": memory_id})
            if "error" not in result:
                print("✅ delete_memory: PASSED")
            else:
                print(f"❌ delete_memory: FAILED - {result}")
        else:
            print("\n12. Skipping delete_memory (no memory_id available)")
    
    print("\n" + "=" * 60)
    print("🏁 MCP Tools Validation Complete")

if __name__ == "__main__":
    asyncio.run(test_all_mcp_tools())
