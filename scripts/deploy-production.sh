#!/bin/bash
# Production Deployment Script for Spark Memory MCP Server
# Two-person team setup with user isolation

set -e

cd "$(dirname "$0")/.."

echo "🚀 Deploying Spark Memory MCP Server - Production"
echo "================================================="

# Check if required files exist
if [[ ! -f ".env.production.user1" ]]; then
    echo "❌ Error: .env.production.user1 not found"
    exit 1
fi

if [[ ! -f ".env.production.user2" ]]; then
    echo "❌ Error: .env.production.user2 not found"
    exit 1
fi

# Stop any existing containers
echo "🛑 Stopping existing production containers..."
docker-compose -f docker-compose.production.yml down 2>/dev/null || true

# Build fresh images
echo "🔨 Building production images..."
docker-compose -f docker-compose.production.yml build --no-cache

# Start production services
echo "🚀 Starting production services..."
docker-compose -f docker-compose.production.yml up -d

# Wait for services to be ready
echo "⏳ Waiting for services to be ready..."
sleep 10

# Health checks
echo "🏥 Checking service health..."
echo "Checking User 1 service (port 8050)..."
if curl -f http://************:8050/health >/dev/null 2>&1; then
    echo "✅ User 1 service is healthy"
else
    echo "❌ User 1 service is not responding"
fi

echo "Checking User 2 service (port 8051)..."
if curl -f http://************:8051/health >/dev/null 2>&1; then
    echo "✅ User 2 service is healthy"
else
    echo "❌ User 2 service is not responding"
fi

echo ""
echo "📊 Deployment Summary:"
echo "======================"
echo "🔗 User 1 (aung-dev):           http://************:8050"
echo "🔗 User 2 (team-member-2):      http://************:8051"
echo "💾 Database:                    Supabase (192.168.1.218:54322)"
echo "🧠 BGE Embeddings:              http://************:8080"
echo "📈 Neo4j Graph:                 bolt://************:7687"
echo ""
echo "📱 Client Configuration Examples:"
echo "=================================="
echo ""
echo "User 1 Claude Desktop config:"
echo '  "spark-memory": {'
echo '    "command": "npx",'
echo '    "args": ["-y", "mcp-remote", "http://************:8050/sse/", "--allow-http"]'
echo '  }'
echo ""
echo "User 2 Claude Desktop config:"
echo '  "spark-memory": {'
echo '    "command": "npx",'
echo '    "args": ["-y", "mcp-remote", "http://************:8051/sse/", "--allow-http"]'
echo '  }'
echo ""

# Show container status
echo "📋 Container Status:"
echo "===================="
docker-compose -f docker-compose.production.yml ps

echo ""
echo "✅ Production deployment complete!"
echo "💡 Tip: Check logs with: docker-compose -f docker-compose.production.yml logs -f"