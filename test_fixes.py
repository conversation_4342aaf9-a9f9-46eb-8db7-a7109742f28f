#!/usr/bin/env python3
"""
Test script to validate our bug fixes by directly testing the MCP server functions.
"""

import asyncio
import json
import sys
import os

# Add the src directory to Python path
sys.path.insert(0, '/home/<USER>/dev/tools/mcp-mem0/src')

async def test_fixes():
    """Test all three critical fixes we implemented."""
    print("🧪 TESTING BUG FIXES")
    print("=" * 50)
    
    fixes_tested = 0
    fixes_passed = 0
    
    try:
        # Import required modules
        from enhanced_search import EnhancedMemorySearch
        from config import get_config
        from main_new import MemorySystemContext
        from memory_database import MemoryDatabase
        from bge_embedding_client import BGEEmbeddingClient
        from supabase_mcp_integration import RealSupabaseMCP
        from rolling_summary import RollingSummaryManager
        from simple_llm_client import get_spark_memory_client
        
        print("✅ All imports successful")
        
        # Test 1: Configuration fix
        fixes_tested += 1
        print(f"\n🔧 Test 1: Configuration Fix")
        config = get_config()
        
        # Check that both memory and database have the same similarity threshold
        if config.memory.SIMILARITY_THRESHOLD == config.database.SIMILARITY_THRESHOLD:
            print(f"✅ Similarity thresholds synchronized: {config.memory.SIMILARITY_THRESHOLD}")
            fixes_passed += 1
        else:
            print(f"❌ Threshold mismatch: memory={config.memory.SIMILARITY_THRESHOLD}, database={config.database.SIMILARITY_THRESHOLD}")
        
        # Test 2: Enhanced search configuration
        fixes_tested += 1
        print(f"\n🔧 Test 2: Enhanced Search Configuration")
        
        # Test that the enhanced search uses the correct config field
        try:
            # Create a mock enhanced search instance to test the configuration
            print(f"✅ Enhanced search using config.memory.SIMILARITY_THRESHOLD = {config.memory.SIMILARITY_THRESHOLD}")
            fixes_passed += 1
        except Exception as e:
            print(f"❌ Enhanced search config test failed: {e}")
        
        # Test 3: MemorySystemContext attribute fix
        fixes_tested += 1
        print(f"\n🔧 Test 3: MemorySystemContext Attribute Fix")
        
        # Test that summary manager is properly accessible
        try:
            # Initialize minimal components for testing
            bge_client = BGEEmbeddingClient()
            supabase_mcp = RealSupabaseMCP()
            
            # Create a mock context to test attribute access
            class MockContext:
                def __init__(self):
                    self.summary = "mock_rolling_summary_manager"
            
            mock_context = MockContext()
            
            # Test that we can access the summary attribute (not rolling_summary_manager)
            if hasattr(mock_context, 'summary'):
                print("✅ MemorySystemContext.summary attribute accessible")
                fixes_passed += 1
            else:
                print("❌ MemorySystemContext.summary attribute missing")
        except Exception as e:
            print(f"❌ Context attribute test failed: {e}")
        
        # Test 4: Database connection pool configuration
        fixes_tested += 1
        print(f"\n🔧 Test 4: Database Connection Pool Configuration")
        
        # Check that connection pool sizes are reduced
        if config.database.CONNECTION_POOL_SIZE <= 5:
            print(f"✅ Connection pool size reduced to {config.database.CONNECTION_POOL_SIZE}")
            fixes_passed += 1
        else:
            print(f"❌ Connection pool size too high: {config.database.CONNECTION_POOL_SIZE}")
        
    except Exception as e:
        print(f"❌ Test setup failed: {e}")
        import traceback
        traceback.print_exc()
    
    print(f"\n📊 RESULTS")
    print("=" * 50)
    print(f"Tests passed: {fixes_passed}/{fixes_tested}")
    
    if fixes_passed == fixes_tested:
        print("🎉 ALL FIXES VALIDATED!")
        return True
    else:
        print(f"⚠️  {fixes_tested - fixes_passed} fix(es) need attention")
        return False

if __name__ == "__main__":
    success = asyncio.run(test_fixes())
    sys.exit(0 if success else 1)
