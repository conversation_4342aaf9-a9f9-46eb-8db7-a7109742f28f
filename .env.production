# Production Environment for spark-memory MCP Server
# This extends the base .env with production-specific overrides

# Environment Setting
ENVIRONMENT=production

# MCP Server Configuration
MCP_USER_ID=user
TRANSPORT=sse
HOST=0.0.0.0
PORT=8050

# LLM Configuration (copy from your main .env)
LLM_PROVIDER=openrouter
LLM_BASE_URL=https://openrouter.ai/api/v1
LLM_API_KEY=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
LLM_CHOICE=anthropic/claude-3.5-sonnet

# BGE Embedding Server (external service)
BGE_SERVER_URL=http://************:8080

# External Supabase Database
DATABASE_URL=**************************************************************************/postgres

# Neo4j Configuration
NEO4J_URI=bolt://host.docker.internal:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=password

# Logging
UVICORN_LOG_LEVEL=info
LOG_LEVEL=INFO
