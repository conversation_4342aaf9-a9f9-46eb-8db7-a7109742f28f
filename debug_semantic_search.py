#!/usr/bin/env python3
"""
Debug script to test semantic search with users who actually have memories
"""

import asyncio
import sys
import os
sys.path.append('src')

from supabase_mcp_integration import RealSupabaseMCP
from bge_embedding_client import BGEEmbeddingClient

async def debug_semantic_search():
    """Test semantic search with users who have actual memories"""
    print("🔍 Testing Semantic Search with Real Data")
    print("=" * 50)
    
    try:
        supabase = RealSupabaseMCP()
        bge_client = BGEEmbeddingClient()
        
        # Test with the main user who has 39 memories
        test_user = "user"
        
        print(f"\n📋 Testing search for user '{test_user}' who has memories...")
        
        # Get a sample memory to test exact match
        sample_memories = await supabase.get_all_memories(test_user, limit=3)
        print(f"✅ Found {len(sample_memories)} sample memories:")
        
        for i, memory in enumerate(sample_memories):
            content_preview = memory.get('content', '')[:80] + "..."
            print(f"  {i+1}. {content_preview}")
        
        if sample_memories:
            # Test 1: Search for a phrase that should definitely be in the memories
            test_queries = [
                "comprehensive test",
                "spark-memory MCP server",
                "BGE embeddings",
                "semantic search",
                "cache hit rate"
            ]
            
            for query in test_queries:
                print(f"\n🔍 Testing query: '{query}'")
                
                query_embedding = await bge_client.embed_single(query, add_instruction=True)
                print(f"✅ Generated {len(query_embedding)}-dim embedding")
                
                # Test with various thresholds
                thresholds = [0.5, 0.7, 1.0, 1.5, 2.0]
                
                for threshold in thresholds:
                    results = await supabase.similarity_search(
                        embedding=query_embedding,
                        user_id=test_user,
                        threshold=threshold,
                        limit=5
                    )
                    
                    print(f"  Threshold {threshold}: {len(results)} results", end="")
                    if results:
                        min_dist = min(r.get('distance', float('inf')) for r in results)
                        max_dist = max(r.get('distance', float('inf')) for r in results)
                        print(f" (distances: {min_dist:.3f}-{max_dist:.3f})")
                        
                        # Show first result
                        first = results[0]
                        content_preview = first.get('content', '')[:60] + "..."
                        print(f"    → {content_preview}")
                    else:
                        print("")
                        
                # If no results at all, try exact match
                if all(len(await supabase.similarity_search(query_embedding, test_user, t, 5)) == 0 for t in thresholds):
                    print(f"  ❌ No results for '{query}' - trying exact content match...")
                    
                    # Try exact match with first memory
                    exact_content = sample_memories[0].get('content', '')
                    exact_embedding = await bge_client.embed_single(exact_content, add_instruction=True)
                    
                    exact_results = await supabase.similarity_search(
                        embedding=exact_embedding,
                        user_id=test_user,
                        threshold=5.0,  # Very liberal
                        limit=3
                    )
                    
                    print(f"  🎯 Exact content match: {len(exact_results)} results")
                    if exact_results:
                        for j, result in enumerate(exact_results[:2]):
                            distance = result.get('distance', 'N/A')
                            print(f"    {j+1}. Distance: {distance:.4f}")
                    else:
                        print("  ❌ CRITICAL: Even exact content match failed!")
                        break
            
        else:
            print("❌ No sample memories available")
            
    except Exception as e:
        print(f"❌ Debug failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(debug_semantic_search())
