#!/usr/bin/env python3
"""
Test the semantic search fix
"""

import asyncio
import sys
import os
sys.path.append('src')

from supabase_mcp_integration import RealSupabaseMCP
from bge_embedding_client import BGEEmbeddingClient

async def test_search_fix():
    """Test the semantic search fix"""
    print("🔧 Testing Semantic Search Fix")
    print("=" * 40)
    
    try:
        supabase = RealSupabaseMCP()
        bge_client = BGEEmbeddingClient()
        
        # Test with the main user who has 39 memories
        test_user = "user"
        
        print(f"\n🔍 Testing search with user '{test_user}'...")
        
        # Test queries that should find results
        test_queries = [
            "comprehensive test",
            "spark-memory MCP server",
            "BGE embeddings",
            "semantic search",
            "cache hit rate"
        ]
        
        for query in test_queries:
            print(f"\n📋 Query: '{query}'")
            
            query_embedding = await bge_client.embed_single(query, add_instruction=True)
            
            # Test with the new threshold (1.2)
            results = await supabase.similarity_search(
                embedding=query_embedding,
                user_id=test_user,
                threshold=1.2,
                limit=5
            )
            
            print(f"✅ Found {len(results)} results with threshold 1.2")
            
            if results:
                for i, result in enumerate(results[:3]):
                    distance = result.get('distance', 'N/A')
                    content_preview = result.get('content', '')[:60] + "..."
                    print(f"  {i+1}. Distance {distance:.3f}: {content_preview}")
            else:
                print("  ❌ Still no results - need higher threshold")
                
                # Try higher threshold
                results_high = await supabase.similarity_search(
                    embedding=query_embedding,
                    user_id=test_user,
                    threshold=1.5,
                    limit=5
                )
                
                print(f"  🔍 With threshold 1.5: {len(results_high)} results")
                if results_high:
                    for i, result in enumerate(results_high[:2]):
                        distance = result.get('distance', 'N/A')
                        content_preview = result.get('content', '')[:50] + "..."
                        print(f"    {i+1}. Distance {distance:.3f}: {content_preview}")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_search_fix())
