# Test Environment Configuration
# This file is used for testing outside Docker containers

# LLM Configuration
LLM_PROVIDER=openrouter
LLM_CHOICE=google/gemini-2.5-flash-lite
LLM_API_KEY=sk-or-v1-b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8
LLM_BASE_URL=https://openrouter.ai/api/v1

# Database Configuration - Use localhost for testing outside Docker
DATABASE_URL=postgresql://postgres:spark_password@localhost:5433/spark_memory

# Neo4j Configuration
NEO4J_URI=bolt://************:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=Ahayh@5096$

# Development Settings
ENVIRONMENT=development
LOG_LEVEL=DEBUG
PYTHONUNBUFFERED=1

# BGE Server Configuration
BGE_SERVER_URL=http://************:8080

# User ID for testing
MCP_USER_ID=test_user
