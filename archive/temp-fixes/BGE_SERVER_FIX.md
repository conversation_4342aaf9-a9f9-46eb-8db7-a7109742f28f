# BGE Server URL Configuration Update

## Important: Update BGE Server URL

Since your BGE server is running on the Ubuntu host at ************:8080, you need to update the BGE server URL in the following files:

### 1. Production docker-compose.yml
Change line 24:
```yaml
# FROM:
BGE_SERVER_URL: http://host.docker.internal:8080

# TO:
BGE_SERVER_URL: http://************:8080
```

### 2. Development Environment
The development docker-compose.dev.yml has already been updated to use:
```yaml
BGE_SERVER_URL: ${BGE_SERVER_URL:-http://************:8080}
```

### 3. Your .env file
Make sure your .env file contains:
```
BGE_SERVER_URL=http://************:8080
```

### 4. Test the BGE Server
Verify the BGE server is accessible:
```bash
curl http://************:8080/health
```

You should see a response like:
```json
{"status": "healthy", "model": "BAAI/bge-base-en-v1.5"}
```

## Note on Docker Networking

- `host.docker.internal` only works on Docker Desktop (Windows/Mac)
- On Linux/Ubuntu servers, you must use the actual IP address
- Since your setup runs on Ubuntu server (************), always use IP addresses

## Quick Fix Command

Run this to update the production docker-compose.yml:
```bash
sed -i 's|http://host.docker.internal:8080|http://************:8080|g' docker-compose.yml
```
