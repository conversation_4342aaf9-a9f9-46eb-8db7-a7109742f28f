#!/usr/bin/env python3
"""
Test semantic search functionality on both development and production containers
"""

import asyncio
import httpx
import json

async def test_mcp_search(port, container_name):
    """Test MCP search endpoint"""
    print(f"\n🔍 Testing {container_name} on port {port}")
    print("=" * 50)
    
    try:
        # Test the add_memories endpoint first
        async with httpx.AsyncClient(timeout=30) as client:
            # Add a test memory
            add_response = await client.post(
                f"http://localhost:{port}/sse/call_tool",
                headers={"Content-Type": "application/json"},
                json={
                    "method": "tools/call",
                    "params": {
                        "name": "add_memories",
                        "arguments": {
                            "text": "Testing the semantic search fix implementation for BGE embeddings with L2 distance threshold",
                            "user_id": "test-semantic-fix"
                        }
                    }
                }
            )
            
            if add_response.status_code == 200:
                print(f"✅ Successfully added test memory")
                
                # Wait a moment for processing
                await asyncio.sleep(2)
                
                # Now test search
                search_response = await client.post(
                    f"http://localhost:{port}/sse/call_tool",
                    headers={"Content-Type": "application/json"},
                    json={
                        "method": "tools/call",
                        "params": {
                            "name": "search_memory", 
                            "arguments": {
                                "query": "semantic search fix",
                                "user_id": "test-semantic-fix",
                                "limit": 3
                            }
                        }
                    }
                )
                
                if search_response.status_code == 200:
                    search_data = search_response.json()
                    print(f"✅ Search request successful")
                    
                    # Try to parse the response
                    if "result" in search_data:
                        result_text = search_data["result"]
                        try:
                            result_json = json.loads(result_text)
                            if result_json.get("success"):
                                results = result_json.get("results", [])
                                print(f"✅ Found {len(results)} search results")
                                
                                if results:
                                    for i, result in enumerate(results[:2]):
                                        if isinstance(result, dict):
                                            content = result.get("content", "")[:60] + "..."
                                            distance = result.get("distance", "N/A")
                                            print(f"  {i+1}. Distance {distance}: {content}")
                                        else:
                                            print(f"  {i+1}. {result[:60]}...")
                                    print(f"🎉 {container_name} semantic search is working!")
                                else:
                                    print(f"❌ {container_name} returned 0 results - fix may not be applied")
                            else:
                                print(f"❌ {container_name} search failed: {result_json.get('error', 'Unknown error')}")
                        except json.JSONDecodeError:
                            print(f"❌ {container_name} returned invalid JSON response")
                    else:
                        print(f"❌ {container_name} unexpected response format")
                else:
                    print(f"❌ {container_name} search request failed: {search_response.status_code}")
            else:
                print(f"❌ {container_name} add memory failed: {add_response.status_code}")
                
    except Exception as e:
        print(f"❌ {container_name} test failed: {e}")

async def main():
    """Test both containers"""
    print("🔧 Testing Semantic Search Fix on Both Containers")
    print("=" * 60)
    
    # Test development container
    await test_mcp_search(8050, "Development Container")
    
    # Test production container  
    await test_mcp_search(8061, "Production Container")
    
    print(f"\n✅ Testing completed!")

if __name__ == "__main__":
    asyncio.run(main())
