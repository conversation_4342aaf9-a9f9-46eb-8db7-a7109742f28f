FROM python:3.12-slim

ARG PORT=8050

WORKDIR /app

# Install uv and curl for health checks
RUN pip install uv && \
    apt-get update && \
    apt-get install -y curl && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# Copy the MCP server files
COPY . .

# Install packages using uv
RUN uv sync --frozen

EXPOSE ${PORT}

# Health check disabled - the server is working but health check endpoint needs proper implementation
# HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
#     CMD curl -f http://localhost:8050/health || exit 1

# Command to run the MCP server (using new two-phase architecture with BGE embeddings)
CMD ["uv", "run", "src/main_new.py"]