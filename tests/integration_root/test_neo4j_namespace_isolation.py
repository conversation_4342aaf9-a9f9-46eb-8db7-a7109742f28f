#!/usr/bin/env python3
"""
Test script to verify Neo4j namespace-based isolation for two users.
Since Neo4j Community Edition doesn't support multiple databases, 
we use namespace prefixes for complete isolation.
"""

import os
import asyncio
from neo4j import GraphDatabase
from datetime import datetime

# Test configurations
configs = [
    {
        "name": "User 1 (aung-dev)",
        "namespace": "user1_aung_dev",
        "test_entity": "Python Programming",
        "user_id": "aung-dev"
    },
    {
        "name": "User 2 (yohnna_ops)",
        "namespace": "user2_yohnna_ops", 
        "test_entity": "JavaScript Development",
        "user_id": "yohnna_ops"
    }
]

# Neo4j connection details
NEO4J_URI = "bolt://************:7687"
NEO4J_USER = "neo4j"
NEO4J_PASSWORD = "Ahayh@5096$"

def get_entity_label(namespace):
    """Get namespace-prefixed Entity label."""
    return f"Entity_{namespace}"

def get_memory_label(namespace):
    """Get namespace-prefixed Memory label."""
    return f"Memory_{namespace}"

def test_namespace_isolation():
    """Test that namespaces provide proper isolation."""
    
    print("🧪 Testing Neo4j Namespace-Based Isolation")
    print("=" * 50)
    
    driver = GraphDatabase.driver(NEO4J_URI, auth=(NEO4J_USER, NEO4J_PASSWORD))
    
    try:
        # Test each namespace
        for config in configs:
            print(f"\n🔹 Testing {config['name']}")
            print(f"   Namespace: {config['namespace']}")
            
            entity_label = get_entity_label(config['namespace'])
            memory_label = get_memory_label(config['namespace'])
            
            with driver.session() as session:
                # Clear any existing test data for this namespace
                session.run(f"MATCH (n:{entity_label}) WHERE n.name CONTAINS 'TestEntity' DELETE n")
                session.run(f"MATCH (n:{memory_label}) WHERE n.id CONTAINS 'test_memory' DELETE n")
                
                # Create test memory
                session.run(f"""
                    CREATE (m:{memory_label} {{
                        id: $memory_id,
                        user_id: $user_id,
                        namespace: $namespace,
                        created_at: datetime()
                    }})
                """, 
                memory_id=f"test_memory_{config['namespace']}",
                user_id=config['user_id'],
                namespace=config['namespace'])
                
                # Create test entity
                result = session.run(f"""
                    CREATE (e:{entity_label} {{
                        name: $name,
                        type: 'TestEntity',
                        user_id: $user_id,
                        namespace: $namespace,
                        created_at: datetime()
                    }})
                    WITH e
                    MATCH (m:{memory_label} {{id: $memory_id, namespace: $namespace}})
                    CREATE (e)-[:MENTIONED_IN]->(m)
                    RETURN e.name as name, e.user_id as user_id, e.namespace as namespace
                """, 
                name=f"TestEntity_{config['test_entity']}",
                user_id=config['user_id'],
                namespace=config['namespace'],
                memory_id=f"test_memory_{config['namespace']}")
                
                record = result.single()
                if record:
                    print(f"   ✅ Created entity: {record['name']}")
                    print(f"   ✅ User ID: {record['user_id']}")
                    print(f"   ✅ Namespace: {record['namespace']}")
                else:
                    print(f"   ❌ Failed to create entity")
                    return False
        
        # Test isolation - check that each namespace only sees its own data
        print(f"\n🔍 Testing Namespace Isolation:")
        
        for i, config in enumerate(configs):
            entity_label = get_entity_label(config['namespace'])
            memory_label = get_memory_label(config['namespace'])
            
            with driver.session() as session:
                # Check entities in this namespace
                result = session.run(f"""
                    MATCH (e:{entity_label} {{namespace: $namespace}}) 
                    WHERE e.name CONTAINS 'TestEntity'
                    RETURN e.name as name, e.user_id as user_id, e.namespace as namespace
                """, namespace=config['namespace'])
                
                entities = list(result)
                print(f"   📊 {config['name']} namespace contains {len(entities)} test entities:")
                
                for entity in entities:
                    print(f"      - {entity['name']} (user: {entity['user_id']}, namespace: {entity['namespace']})")
                
                # Verify this namespace only sees its own data
                own_entities = [e for e in entities if e['namespace'] == config['namespace']]
                other_entities = [e for e in entities if e['namespace'] != config['namespace']]
                
                if len(own_entities) == 1 and len(other_entities) == 0:
                    print(f"   ✅ {config['name']} isolation: PASS")
                else:
                    print(f"   ❌ {config['name']} isolation: FAIL")
                    print(f"      Own entities: {len(own_entities)}, Other entities: {len(other_entities)}")
                    return False
        
        # Cross-namespace verification - ensure no cross-contamination
        print(f"\n🔍 Cross-Namespace Verification:")
        
        with driver.session() as session:
            # Try to find entities from other namespaces
            for i, config in enumerate(configs):
                other_config = configs[1-i]  # Get the other config
                entity_label = get_entity_label(config['namespace'])
                
                result = session.run(f"""
                    MATCH (e:{entity_label} {{namespace: $other_namespace}}) 
                    WHERE e.name CONTAINS 'TestEntity'
                    RETURN count(e) as count
                """, other_namespace=other_config['namespace'])
                
                record = result.single()
                cross_namespace_count = record['count'] if record else 0
                
                if cross_namespace_count == 0:
                    print(f"   ✅ {config['name']} cannot see {other_config['name']} data: PASS")
                else:
                    print(f"   ❌ {config['name']} can see {other_config['name']} data: FAIL ({cross_namespace_count} entities)")
                    return False
        
        print(f"\n🎉 All namespace isolation tests PASSED!")
        print(f"✅ Namespace-based isolation is working correctly")
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        return False
        
    finally:
        driver.close()

def cleanup_test_data():
    """Clean up test data from all namespaces."""
    print("\n🧹 Cleaning up test data...")
    
    driver = GraphDatabase.driver(NEO4J_URI, auth=(NEO4J_USER, NEO4J_PASSWORD))
    
    try:
        for config in configs:
            entity_label = get_entity_label(config['namespace'])
            memory_label = get_memory_label(config['namespace'])
            
            with driver.session() as session:
                # Delete test entities
                result = session.run(f"""
                    MATCH (e:{entity_label}) 
                    WHERE e.name CONTAINS 'TestEntity' 
                    DELETE e 
                    RETURN count(e) as deleted
                """)
                record = result.single()
                entity_count = record['deleted'] if record else 0
                
                # Delete test memories
                result = session.run(f"""
                    MATCH (m:{memory_label}) 
                    WHERE m.id CONTAINS 'test_memory' 
                    DELETE m 
                    RETURN count(m) as deleted
                """)
                record = result.single()
                memory_count = record['deleted'] if record else 0
                
                print(f"   🗑️  Deleted {entity_count} entities and {memory_count} memories from {config['namespace']}")
    
    except Exception as e:
        print(f"   ⚠️  Cleanup error: {e}")
    
    finally:
        driver.close()

if __name__ == "__main__":
    try:
        success = test_namespace_isolation()
        cleanup_test_data()
        
        if success:
            print(f"\n✅ Neo4j namespace isolation setup is ready for production!")
            print(f"\n📋 Next Steps:")
            print(f"   1. Update .env.dev to set NEO4J_NAMESPACE=user1_aung_dev")
            print(f"   2. Deploy production with separate NEO4J_NAMESPACE values")
            print(f"   3. Each user will have completely isolated graph data")
            exit(0)
        else:
            print(f"\n❌ Neo4j namespace isolation setup needs attention!")
            exit(1)
            
    except KeyboardInterrupt:
        print(f"\n🛑 Test interrupted by user")
        cleanup_test_data()
        exit(1)