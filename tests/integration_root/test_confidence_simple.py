#!/usr/bin/env python3
"""
Simple test for confidence scoring functionality.

Tests confidence calculation and database integration.
"""

import asyncio
import json
import sys
import os

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from confidence_calculator import ConfidenceCalculator
from simple_llm_client import SimpleLLMClient
from memory_extraction import MemoryExtractionModule
from bge_embedding_client import BGEEmbeddingClient

async def test_confidence_extraction():
    """Test confidence scoring in memory extraction."""
    print("🚀 Testing Confidence Scoring in Memory Extraction")
    print("=" * 60)
    
    try:
        # Initialize components
        os.environ['LLM_API_KEY'] = "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"
        os.environ['LLM_BASE_URL'] = "https://openrouter.ai/api/v1"
        
        llm_client = SimpleLLMClient(provider="openrouter", model="google/gemini-2.5-flash-lite")
        bge_client = BGEEmbeddingClient()
        
        # Initialize memory extraction module (now includes confidence calculator)
        extraction_module = MemoryExtractionModule(llm_client, bge_client)
        
        print("✅ Initialized memory extraction module with confidence scoring")
        
        # Test message pairs with different confidence levels
        test_cases = [
            {
                "name": "High Confidence - Specific Facts",
                "messages": (
                    "What's your job?",
                    "I work as a Senior Software Engineer at TechCorp since January 2023. I specialize in Python and React development."
                ),
                "expected_confidence": "high"
            },
            {
                "name": "Low Confidence - Uncertain Preferences", 
                "messages": (
                    "Do you like coffee?",
                    "I think I might prefer tea over coffee, but I'm not really sure about it."
                ),
                "expected_confidence": "low"
            },
            {
                "name": "Medium Confidence - General Statements",
                "messages": (
                    "How do you usually work?",
                    "I typically work from home and prefer morning hours for coding tasks."
                ),
                "expected_confidence": "medium"
            }
        ]
        
        context = {
            "conversation_summary": "User discussing work and preferences",
            "recent_messages": [],
            "user_id": "test_user"
        }
        
        for test_case in test_cases:
            print(f"\n--- {test_case['name']} ---")
            print(f"Messages: {test_case['messages'][0]} -> {test_case['messages'][1]}")
            
            # Extract memories with confidence
            memories_with_confidence = await extraction_module.extract_memories_with_confidence(
                test_case['messages'], context
            )
            
            if memories_with_confidence:
                for i, memory in enumerate(memories_with_confidence):
                    confidence = memory['confidence_score']
                    content = memory['content']
                    factors = memory['confidence_factors']
                    
                    print(f"  Memory {i+1}: {content}")
                    print(f"  Confidence: {confidence:.3f} ({test_case['expected_confidence']} expected)")
                    
                    if 'reasoning' in factors:
                        print(f"  Reasoning: {factors['reasoning']}")
                    
                    # Validate confidence range
                    if test_case['expected_confidence'] == 'high' and confidence >= 0.7:
                        print("  ✅ High confidence correctly identified")
                    elif test_case['expected_confidence'] == 'low' and confidence <= 0.4:
                        print("  ✅ Low confidence correctly identified")
                    elif test_case['expected_confidence'] == 'medium' and 0.4 < confidence < 0.7:
                        print("  ✅ Medium confidence correctly identified")
                    else:
                        print(f"  ⚠️  Confidence level may not match expectation")
            else:
                print("  ❌ No memories extracted")
        
        print("\n🎉 Confidence Extraction Tests Completed!")
        print("✅ Memory extraction with confidence scoring is working")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_confidence_patterns():
    """Test confidence calculation patterns."""
    print("\n🔍 Testing Confidence Calculation Patterns")
    print("=" * 60)
    
    try:
        # Initialize confidence calculator
        llm_client = SimpleLLMClient(provider="mock", model="test")
        calculator = ConfidenceCalculator(llm_client)
        
        # Test different confidence patterns
        test_patterns = [
            ("I definitely know that Python is my favorite programming language", "high certainty"),
            ("My email <NAME_EMAIL>", "specific information"),
            ("I was born on 1990-05-15 in New York City", "specific facts with dates"),
            ("I think I might like working remotely", "uncertain preference"),
            ("Maybe I should learn JavaScript", "uncertain intention"),
            ("I usually work from 9 AM to 5 PM", "general routine"),
            ("I work at TechCorp as a Senior Developer", "job information"),
            ("I guess I prefer coffee", "uncertain preference")
        ]
        
        for content, description in test_patterns:
            confidence_score, factors = await calculator.calculate_initial_confidence(
                memory_content=content,
                extraction_context="Test context",
                source_type="user_input"
            )
            
            print(f"\n{description}:")
            print(f"  Content: {content}")
            print(f"  Confidence: {confidence_score:.3f}")
            print(f"  Certainty: {factors.certainty_score:.2f}")
            print(f"  Specificity: {factors.specificity_score:.2f}")
            print(f"  Reasoning: {factors.reasoning}")
        
        print("\n✅ Confidence pattern tests completed")
        return True
        
    except Exception as e:
        print(f"❌ Pattern test failed: {e}")
        return False

def main():
    """Run all confidence tests."""
    print("🚀 Starting Confidence Scoring Tests")
    print("=" * 60)
    
    success1 = asyncio.run(test_confidence_patterns())
    success2 = asyncio.run(test_confidence_extraction())
    
    if success1 and success2:
        print("\n" + "=" * 60)
        print("🎉 All Confidence Scoring Tests Passed!")
        print("✅ Phase 2 Feature 2/3: Confidence Scores - IMPLEMENTED SUCCESSFULLY")
        print("✅ Ready to commit and proceed to Feature 3/3")
    else:
        print("\n❌ Some tests failed - please review and fix issues")

if __name__ == "__main__":
    main()
