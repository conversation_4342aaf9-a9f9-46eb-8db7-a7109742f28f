#!/usr/bin/env python3
"""
Test MCP Server Functionality

Tests the actual MCP server tools to ensure they work correctly.
"""

import asyncio
import json
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from dotenv import load_dotenv
load_dotenv('.env.test')

from mcp.server.fastmcp import FastMCP, Context
from main_new import memory_system_lifespan

async def test_mcp_server_tools():
    """Test MCP server tools functionality."""
    print("🚀 Testing MCP Server Tools")
    print("=" * 50)
    
    # Create a mock FastMCP server
    server = FastMCP("test-server")
    
    # Initialize memory system
    async with memory_system_lifespan(server) as memory_system:
        print("✅ Memory system initialized successfully")
        
        # Create a mock context
        class MockContext:
            def __init__(self, memory_system):
                self.request_context = type('obj', (object,), {'lifespan_context': memory_system})()
        
        ctx = MockContext(memory_system)
        
        # Test 1: Add memory
        print("\n🔍 Testing add_memories...")
        try:
            from main_new import add_memories
            result = await add_memories(
                ctx,
                text="This is a test memory for the MCP server functionality test."
            )
            result_data = json.loads(result)
            if result_data.get("success"):
                print("✅ add_memories successful")
                memory_id = result_data.get("memory_id")
            else:
                print(f"❌ add_memories failed: {result_data.get('error')}")
                return False
        except Exception as e:
            print(f"❌ add_memories crashed: {e}")
            return False
        
        # Test 2: Search memory
        print("\n🔍 Testing search_memory...")
        try:
            from main_new import search_memory
            result = await search_memory(
                ctx,
                query="test memory functionality"
            )
            result_data = json.loads(result)
            if result_data.get("success"):
                print("✅ search_memory successful")
                print(f"Found {len(result_data.get('memories', []))} memories")
            else:
                print(f"❌ search_memory failed: {result_data.get('error')}")
                return False
        except Exception as e:
            print(f"❌ search_memory crashed: {e}")
            return False
        
        # Test 3: Session management
        print("\n🔍 Testing session management...")
        try:
            from main_new import create_session, get_active_session

            # Create session
            session_result = await create_session(
                ctx,
                session_name="Test MCP Session"
            )
            session_data = json.loads(session_result)
            if session_data.get("success"):
                print("✅ create_session successful")
                session_id = session_data.get("session_id")
            else:
                print(f"❌ create_session failed: {session_data.get('error')}")
                return False

            # Get active session
            info_result = await get_active_session(ctx)
            info_data = json.loads(info_result)
            if info_data.get("success"):
                print("✅ get_active_session successful")
            else:
                print(f"❌ get_active_session failed: {info_data.get('error')}")
                return False

        except Exception as e:
            print(f"❌ Session management crashed: {e}")
            return False
        
        # Test 4: Entity-based search
        print("\n🔍 Testing entity-based search...")
        try:
            from main_new import search_by_entity
            result = await search_by_entity(
                ctx,
                entity_name="test"
            )
            result_data = json.loads(result)
            if result_data.get("success"):
                print("✅ search_by_entity successful")
            else:
                print(f"❌ search_by_entity failed: {result_data.get('error')}")
                return False
        except Exception as e:
            print(f"❌ Entity-based search crashed: {e}")
            return False
        
        print("\n" + "=" * 50)
        print("🎉 ALL MCP SERVER TOOLS WORKING!")
        print("=" * 50)
        return True

if __name__ == "__main__":
    asyncio.run(test_mcp_server_tools())
