#!/usr/bin/env python3
"""
Critical Functionality Test Script for Spark Memory MCP

Tests all Phase 1 and Phase 2 features to ensure they work correctly
in both development and production environments.
"""

import asyncio
import json
import os
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from enhanced_database_service import EnhancedDatabaseService
from session_manager import SessionManager
from graph_memory_service import GraphMemoryService
from simple_llm_client import get_spark_memory_client
from config import get_config

async def test_database_connection():
    """Test PostgreSQL database connection and schema."""
    print("🔍 Testing database connection...")
    
    db_service = EnhancedDatabaseService(
        database_url=os.getenv('DATABASE_URL'),
        min_connections=1,
        max_connections=2
    )
    
    try:
        await db_service.initialize()
        print("✅ Database connection successful")
        
        # Test basic query
        result = await db_service.execute_sql("SELECT 1 as test")
        if result and result[0]['test'] == 1:
            print("✅ Database query execution successful")
        else:
            print("❌ Database query failed")
            return False
            
        # Test memories table with session_id
        result = await db_service.execute_sql(
            "SELECT column_name FROM information_schema.columns WHERE table_name = 'memories' AND column_name = 'session_id'"
        )
        if result:
            print("✅ memories table has session_id column")
        else:
            print("❌ memories table missing session_id column")
            return False
            
        await db_service.close()
        return True
        
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False

async def test_session_management():
    """Test session management functionality."""
    print("\n🔍 Testing session management...")
    
    try:
        db_service = EnhancedDatabaseService(
            database_url=os.getenv('DATABASE_URL'),
            min_connections=1,
            max_connections=2
        )
        await db_service.initialize()
        
        session_manager = SessionManager(db_service)
        
        # Test session creation
        session = await session_manager.create_session(
            user_id="test_user",
            session_name="Test Session",
            metadata={"test": True}
        )
        session_id = session.id
        print(f"✅ Session created: {session_id}")

        # Test session retrieval
        retrieved_session = await session_manager.get_session(session_id)
        if retrieved_session and retrieved_session.id == session_id:
            print("✅ Session retrieval successful")
        else:
            print("❌ Session retrieval failed")
            return False

        # Test session ending
        await session_manager.end_session(session_id)
        print("✅ Session ended successfully")
        
        await db_service.close()
        return True
        
    except Exception as e:
        print(f"❌ Session management test failed: {e}")
        return False

async def test_graph_service():
    """Test Neo4j graph service connection."""
    print("\n🔍 Testing Neo4j graph service...")

    try:
        # Debug Neo4j configuration
        from config import get_neo4j_config
        neo4j_config = get_neo4j_config()
        print(f"Neo4j URI: {neo4j_config['uri']}")
        print(f"Neo4j User: {neo4j_config['username']}")
        print(f"Neo4j Password: {'*' * len(neo4j_config['password'])}")

        llm_client = get_spark_memory_client()
        graph_service = GraphMemoryService(llm_client)

        # Test initialization
        await graph_service.initialize()
        print("✅ Graph service initialized")

        # Test connection
        result = await graph_service.test_connection()
        if result and "successful" in result.get("message", ""):
            print("✅ Neo4j connection test successful")
        else:
            print("❌ Neo4j connection test failed")
            return False

        return True

    except Exception as e:
        print(f"❌ Graph service test failed: {e}")
        return False

async def test_semantic_search():
    """Test semantic search with session support."""
    print("\n🔍 Testing semantic search...")
    
    try:
        db_service = EnhancedDatabaseService(
            database_url=os.getenv('DATABASE_URL'),
            min_connections=1,
            max_connections=2
        )
        await db_service.initialize()
        
        # Test similarity search query (should not crash)
        sql = """
        SELECT m.id, m.content, m.user_id, m.session_id, m.confidence_score,
               (m.embedding <-> $1::vector) as distance
        FROM memories m
        WHERE m.user_id = $2
        ORDER BY m.embedding <-> $1::vector
        LIMIT 5
        """
        
        # Create a dummy embedding vector (768 dimensions for BGE)
        dummy_embedding = [0.1] * 768
        
        result = await db_service.execute_sql(sql, [dummy_embedding, "test_user"])
        print("✅ Semantic search query executed successfully")
        
        await db_service.close()
        return True
        
    except Exception as e:
        print(f"❌ Semantic search test failed: {e}")
        return False

async def main():
    """Run all critical functionality tests."""
    print("🚀 Starting Critical Functionality Tests for Spark Memory MCP")
    print("=" * 60)
    
    # Load environment
    from dotenv import load_dotenv
    load_dotenv('.env.dev')
    
    tests = [
        ("Database Connection", test_database_connection),
        ("Session Management", test_session_management),
        ("Graph Service", test_graph_service),
        ("Semantic Search", test_semantic_search),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results[test_name] = result
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results[test_name] = False
    
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    all_passed = True
    for test_name, passed in results.items():
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{test_name}: {status}")
        if not passed:
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 ALL CRITICAL TESTS PASSED!")
        print("The Spark Memory MCP system is fully operational.")
    else:
        print("⚠️  SOME TESTS FAILED!")
        print("Critical issues still need to be resolved.")
    print("=" * 60)
    
    return all_passed

if __name__ == "__main__":
    asyncio.run(main())
