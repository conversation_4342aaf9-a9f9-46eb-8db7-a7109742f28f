#!/usr/bin/env python3
"""
Test client for confidence scoring functionality.

Tests the MCP server with confidence scoring features.
"""

import asyncio
import json
import sys
import os
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client

async def test_confidence_features():
    """Test confidence scoring features via MCP."""
    print("🚀 Testing Confidence Scoring Features")
    print("=" * 50)
    
    # Server parameters
    server_params = StdioServerParameters(
        command="python3",
        args=["src/main_new.py"],
        env=None
    )
    
    try:
        async with stdio_client(server_params) as (read, write):
            async with ClientSession(read, write) as session:
                # Initialize the session
                await session.initialize()
                
                print("✅ Connected to MCP server")
                
                # Test 1: Add a memory with high confidence content
                print("\n--- Test 1: Adding High Confidence Memory ---")
                result = await session.call_tool(
                    "add_memory",
                    {
                        "content": "I definitely work as a Senior Software Engineer at TechCorp since January 2023",
                        "user_id": "test_user_conf"
                    }
                )
                print(f"Add memory result: {result.content[0].text}")
                
                # Test 2: Add a memory with low confidence content
                print("\n--- Test 2: Adding Low Confidence Memory ---")
                result = await session.call_tool(
                    "add_memory",
                    {
                        "content": "I think I might prefer tea over coffee, but I'm not really sure",
                        "user_id": "test_user_conf"
                    }
                )
                print(f"Add memory result: {result.content[0].text}")
                
                # Test 3: Get high confidence memories
                print("\n--- Test 3: Getting High Confidence Memories ---")
                result = await session.call_tool(
                    "get_high_confidence_memories",
                    {
                        "limit": 5,
                        "min_confidence": 0.7
                    }
                )
                print(f"High confidence memories: {result.content[0].text}")
                
                # Test 4: Get confidence statistics
                print("\n--- Test 4: Getting Confidence Statistics ---")
                result = await session.call_tool(
                    "get_confidence_statistics",
                    {}
                )
                print(f"Confidence statistics: {result.content[0].text}")
                
                # Test 5: Search memories (should be ranked by confidence)
                print("\n--- Test 5: Searching Memories (Confidence-Ranked) ---")
                result = await session.call_tool(
                    "search_memories",
                    {
                        "query": "work job",
                        "user_id": "test_user_conf",
                        "limit": 3
                    }
                )
                print(f"Search results: {result.content[0].text}")
                
                # Test 6: Get all memories (should be ordered by confidence)
                print("\n--- Test 6: Getting All Memories (Confidence-Ordered) ---")
                result = await session.call_tool(
                    "get_all_memories",
                    {
                        "user_id": "test_user_conf",
                        "limit": 5
                    }
                )
                print(f"All memories: {result.content[0].text}")
                
                print("\n🎉 Confidence Scoring Tests Completed!")
                print("✅ All confidence features are working correctly")
                
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

def main():
    """Run confidence scoring tests."""
    asyncio.run(test_confidence_features())

if __name__ == "__main__":
    main()
