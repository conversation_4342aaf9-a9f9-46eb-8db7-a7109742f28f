#!/usr/bin/env python3
"""
Test Neo4j Graph Service in isolation
"""

import asyncio
import sys
import logging
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Set up logging
logging.basicConfig(level=logging.DEBUG)

from dotenv import load_dotenv
load_dotenv('.env.dev')

from graph_memory_service import GraphMemoryService
from simple_llm_client import get_spark_memory_client

async def test_graph_service_detailed():
    """Test graph service with detailed debugging."""
    print("🔍 Testing Neo4j graph service in isolation...")
    
    try:
        print("1. Getting LLM client...")
        llm_client = get_spark_memory_client()
        print("✅ LLM client obtained")
        
        print("2. Creating GraphMemoryService...")
        graph_service = GraphMemoryService(llm_client)
        print("✅ GraphMemoryService created")
        
        print("3. Initializing graph service...")
        await graph_service.initialize()
        print("✅ Graph service initialized")
        
        print("4. Testing connection...")
        result = await graph_service.test_connection()
        print(f"✅ Connection test result: {result}")
        
        return True
        
    except Exception as e:
        print(f"❌ Graph service test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    asyncio.run(test_graph_service_detailed())
