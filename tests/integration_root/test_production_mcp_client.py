#!/usr/bin/env python3
"""
Test Production MCP Server via HTTP

Tests the production MCP server running in Docker to ensure all functionality works.
"""

import asyncio
import json
import aiohttp
import sys
from pathlib import Path

async def test_production_mcp_server():
    """Test production MCP server via HTTP requests."""
    print("🚀 Testing Production MCP Server")
    print("=" * 50)
    
    base_url = "http://localhost:8050"
    
    async with aiohttp.ClientSession() as session:
        
        # Test 1: Health check via MCP
        print("\n🔍 Testing health check via MCP...")
        try:
            mcp_request = {
                "jsonrpc": "2.0",
                "id": 0,
                "method": "tools/call",
                "params": {
                    "name": "health_check",
                    "arguments": {}
                }
            }

            async with session.post(
                f"{base_url}/mcp",
                json=mcp_request,
                headers={"Content-Type": "application/json"}
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    if "result" in result:
                        health_result = json.loads(result["result"]["content"][0]["text"])
                        print("✅ Health check successful")
                        print(f"   Status: {health_result.get('status')}")
                        print(f"   Service: {health_result.get('service')}")
                    else:
                        print(f"❌ MCP health check failed: {result}")
                        return False
                else:
                    print(f"❌ HTTP request failed: {response.status}")
                    return False
        except Exception as e:
            print(f"❌ Health check crashed: {e}")
            return False
        
        # Test 2: Add memory via MCP
        print("\n🔍 Testing add_memories via MCP...")
        try:
            mcp_request = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "tools/call",
                "params": {
                    "name": "add_memories",
                    "arguments": {
                        "text": "This is a test memory for production MCP server validation.",
                        "session_id": None
                    }
                }
            }
            
            async with session.post(
                f"{base_url}/mcp",
                json=mcp_request,
                headers={"Content-Type": "application/json"}
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    if "result" in result:
                        memory_result = json.loads(result["result"]["content"][0]["text"])
                        if memory_result.get("success"):
                            print("✅ add_memories successful")
                            print(f"   Memory ID: {memory_result.get('memory_id')}")
                        else:
                            print(f"❌ add_memories failed: {memory_result.get('error')}")
                            return False
                    else:
                        print(f"❌ MCP request failed: {result}")
                        return False
                else:
                    print(f"❌ HTTP request failed: {response.status}")
                    return False
        except Exception as e:
            print(f"❌ add_memories crashed: {e}")
            return False
        
        # Test 3: Search memory via MCP
        print("\n🔍 Testing search_memory via MCP...")
        try:
            mcp_request = {
                "jsonrpc": "2.0",
                "id": 2,
                "method": "tools/call",
                "params": {
                    "name": "search_memory",
                    "arguments": {
                        "query": "test memory production validation",
                        "limit": 5
                    }
                }
            }
            
            async with session.post(
                f"{base_url}/mcp",
                json=mcp_request,
                headers={"Content-Type": "application/json"}
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    if "result" in result:
                        search_result = json.loads(result["result"]["content"][0]["text"])
                        if search_result.get("success"):
                            print("✅ search_memory successful")
                            print(f"   Found {len(search_result.get('memories', []))} memories")
                        else:
                            print(f"❌ search_memory failed: {search_result.get('error')}")
                            return False
                    else:
                        print(f"❌ MCP request failed: {result}")
                        return False
                else:
                    print(f"❌ HTTP request failed: {response.status}")
                    return False
        except Exception as e:
            print(f"❌ search_memory crashed: {e}")
            return False
        
        # Test 4: Session management via MCP
        print("\n🔍 Testing session management via MCP...")
        try:
            mcp_request = {
                "jsonrpc": "2.0",
                "id": 3,
                "method": "tools/call",
                "params": {
                    "name": "create_session",
                    "arguments": {
                        "session_name": "Production Test Session"
                    }
                }
            }
            
            async with session.post(
                f"{base_url}/mcp",
                json=mcp_request,
                headers={"Content-Type": "application/json"}
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    if "result" in result:
                        session_result = json.loads(result["result"]["content"][0]["text"])
                        if session_result.get("success"):
                            print("✅ create_session successful")
                            print(f"   Session ID: {session_result.get('session_id')}")
                        else:
                            print(f"❌ create_session failed: {session_result.get('error')}")
                            return False
                    else:
                        print(f"❌ MCP request failed: {result}")
                        return False
                else:
                    print(f"❌ HTTP request failed: {response.status}")
                    return False
        except Exception as e:
            print(f"❌ Session management crashed: {e}")
            return False
        
        print("\n" + "=" * 50)
        print("🎉 PRODUCTION MCP SERVER FULLY FUNCTIONAL!")
        print("=" * 50)
        return True

if __name__ == "__main__":
    asyncio.run(test_production_mcp_server())
