#!/usr/bin/env python3
"""
Test script to verify Neo4j database isolation for two users.
"""

import os
import asyncio
from neo4j import GraphDatabase
from datetime import datetime

# Test configurations
configs = [
    {
        "name": "User 1 (aung-dev)",
        "database": "user1_aung_dev",
        "test_entity": "Python Programming",
        "user_id": "aung-dev"
    },
    {
        "name": "User 2 (team-member-2)",
        "database": "user2_team_member", 
        "test_entity": "JavaScript Development",
        "user_id": "<EMAIL>"
    }
]

# Neo4j connection details
NEO4J_URI = "bolt://************:7687"
NEO4J_USER = "neo4j"
NEO4J_PASSWORD = "Ahayh@5096$"

def test_database_isolation():
    """Test that databases are properly isolated."""
    
    print("🧪 Testing Neo4j Database Isolation")
    print("=" * 50)
    
    driver = GraphDatabase.driver(NEO4J_URI, auth=(NEO4J_USER, NEO4J_PASSWORD))
    
    try:
        # Test each database
        for config in configs:
            print(f"\n🔹 Testing {config['name']}")
            print(f"   Database: {config['database']}")
            
            with driver.session(database=config['database']) as session:
                # Clear any existing test data
                session.run("MATCH (n:TestEntity) DELETE n")
                
                # Create test entity
                result = session.run("""
                    CREATE (e:TestEntity {
                        name: $name,
                        user_id: $user_id,
                        created_at: datetime(),
                        test_run: $test_run
                    })
                    RETURN e.name as name, e.user_id as user_id
                """, 
                name=config['test_entity'],
                user_id=config['user_id'],
                test_run=datetime.now().isoformat())
                
                record = result.single()
                if record:
                    print(f"   ✅ Created entity: {record['name']}")
                    print(f"   ✅ User ID: {record['user_id']}")
                else:
                    print(f"   ❌ Failed to create entity")
                    return False
        
        # Test isolation - check that User 1 can't see User 2's data
        print(f"\n🔍 Testing Isolation:")
        
        with driver.session(database="user1_aung_dev") as session:
            # Should find User 1's entity
            result = session.run("MATCH (e:TestEntity) RETURN e.name as name, e.user_id as user_id")
            records = list(result)
            
            print(f"   📊 User 1 database contains {len(records)} entities:")
            for record in records:
                print(f"      - {record['name']} (user: {record['user_id']})")
            
            # Verify User 1 only sees their own data
            user1_entities = [r for r in records if r['user_id'] == 'aung-dev']
            user2_entities = [r for r in records if r['user_id'] == '<EMAIL>']
            
            if len(user1_entities) == 1 and len(user2_entities) == 0:
                print(f"   ✅ User 1 isolation: PASS")
            else:
                print(f"   ❌ User 1 isolation: FAIL")
                return False
        
        with driver.session(database="user2_team_member") as session:
            # Should find User 2's entity
            result = session.run("MATCH (e:TestEntity) RETURN e.name as name, e.user_id as user_id")
            records = list(result)
            
            print(f"   📊 User 2 database contains {len(records)} entities:")
            for record in records:
                print(f"      - {record['name']} (user: {record['user_id']})")
            
            # Verify User 2 only sees their own data
            user1_entities = [r for r in records if r['user_id'] == 'aung-dev']
            user2_entities = [r for r in records if r['user_id'] == '<EMAIL>']
            
            if len(user2_entities) == 1 and len(user1_entities) == 0:
                print(f"   ✅ User 2 isolation: PASS")
            else:
                print(f"   ❌ User 2 isolation: FAIL")
                return False
        
        print(f"\n🎉 All isolation tests PASSED!")
        print(f"✅ Database isolation is working correctly")
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        return False
        
    finally:
        driver.close()

def cleanup_test_data():
    """Clean up test data from both databases."""
    print("\n🧹 Cleaning up test data...")
    
    driver = GraphDatabase.driver(NEO4J_URI, auth=(NEO4J_USER, NEO4J_PASSWORD))
    
    try:
        for config in configs:
            with driver.session(database=config['database']) as session:
                result = session.run("MATCH (n:TestEntity) DELETE n RETURN count(n) as deleted")
                record = result.single()
                if record:
                    print(f"   🗑️  Deleted {record['deleted']} test entities from {config['database']}")
    
    except Exception as e:
        print(f"   ⚠️  Cleanup error: {e}")
    
    finally:
        driver.close()

if __name__ == "__main__":
    try:
        success = test_database_isolation()
        cleanup_test_data()
        
        if success:
            print(f"\n✅ Neo4j isolation setup is ready for production!")
            exit(0)
        else:
            print(f"\n❌ Neo4j isolation setup needs attention!")
            exit(1)
            
    except KeyboardInterrupt:
        print(f"\n🛑 Test interrupted by user")
        cleanup_test_data()
        exit(1)