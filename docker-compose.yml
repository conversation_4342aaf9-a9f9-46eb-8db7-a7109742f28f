version: '3.8'

services:
  # ==========================================
  # Spark Memory MCP Server - Production
  # ==========================================
  spark-mcp:
    build: .
    container_name: spark-mcp-server
    ports:
      - "8061:8050"
    environment:
      # MCP Server Configuration
      TRANSPORT: ${TRANSPORT:-sse}
      HOST: ${HOST:-0.0.0.0}
      PORT: ${PORT:-8050}

      # LLM Configuration (from .env)
      LLM_PROVIDER: ${LLM_PROVIDER}
      LLM_API_KEY: ${LLM_API_KEY}
      LLM_CHOICE: ${LLM_CHOICE}
      LLM_BASE_URL: ${LLM_BASE_URL}

      # BGE Embedding Server (external service)
      BGE_SERVER_URL: ${BGE_SERVER_URL}

      # External Supabase Database
      DATABASE_URL: ${DATABASE_URL}

      # Neo4j Configuration
      NEO4J_URI: ${NEO4J_URI}
      NEO4J_USER: ${NEO4J_USER}
      NEO4J_PASSWORD: ${NEO4J_PASSWORD}

      # Legacy compatibility (unused in new architecture)
      EMBEDDING_MODEL_CHOICE: ${EMBEDDING_MODEL_CHOICE:-text-embedding-3-small}

      # Logging configuration
      UVICORN_LOG_LEVEL: ${UVICORN_LOG_LEVEL:-info}
    extra_hosts:
      - "host.docker.internal:host-gateway"
    networks:
      - spark-network
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "sh", "-c", "curl -s --max-time 3 -o /dev/null -w '%{http_code}' http://localhost:8050/sse | grep -q '^200$' || exit 1"]
      interval: 10s
      timeout: 15s
      retries: 10
      start_period: 120s



# ==========================================
# Networks
# ==========================================
networks:
  spark-network:
    driver: bridge
    name: spark-network

# ==========================================
# Usage Examples:
# ==========================================
#
# PRODUCTION deployment:
# docker-compose up -d
#
# Health check:
# docker-compose ps
# curl http://localhost:8050/sse
#
# View logs:
# docker-compose logs -f spark-mcp
#
# IMPORTANT NOTES:
# - Uses external Supabase database and BGE embedding server
# - Uses external Neo4j instance for graph operations
# - All configuration comes from .env file
# - No mock services - production ready only
#