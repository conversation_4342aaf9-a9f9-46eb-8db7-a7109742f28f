#!/usr/bin/env python3
"""
Test script for AI hallucination detection.
This script intentionally contains both real and potentially hallucinated code.
"""

import requests  # Real import
import faker     # Real import  
import nonexistent_lib  # Fake import - should be flagged

from typing_extensions import TypedDict, Literal  # Real import
from requests.models import Response  # Real import
from faker.providers import BaseProvider  # Real import
from requests.adapters import HTTPAdapter  # Real import

# Test real vs fake method calls
def test_requests():
    # Real method calls
    response = requests.get("https://api.example.com")
    response.raise_for_status()  # Real method
    
    # Potentially fake method calls  
    response.auto_retry()  # Fake method - requests doesn't have this
    response.smart_parse_json()  # Fake method
    
    # Real method calls
    session = requests.Session()
    adapter = HTTPAdapter(max_retries=3)
    session.mount("https://", adapter)

def test_faker():
    fake = faker.Faker()
    
    # Real method calls
    name = fake.name()
    email = fake.email()
    address = fake.address()
    
    # Potentially fake method calls
    social_security = fake.social_security_number()  # May or may not exist
    fake_method = fake.generate_random_data()  # Fake method
    
    # Real provider usage
    fake.add_provider(BaseProvider)

def test_typing_extensions():
    # Real usage
    MyDict = TypedDict('MyDict', {'name': str, 'age': int})
    Status = Literal['pending', 'approved', 'rejected']
    
    # Fake usage
    AdvancedType = TypedDict.with_defaults({'name': str})  # Fake method

class TestClass:
    def __init__(self):
        self.session = requests.Session()
        self.faker = faker.Faker()
    
    def real_method(self):
        return self.session.get("https://example.com")
    
    def fake_method_call(self):
        # This uses a fake method on a real class
        return self.session.auto_configure_ssl()  # Fake method

if __name__ == "__main__":
    test_requests()
    test_faker()
    test_typing_extensions()
