#!/bin/bash
# Start Spark Memory MCP Server for aung-dev user

# Navigate to project directory
cd "$(dirname "$0")/.."

# Set user-specific environment variables
export MCP_USER_ID="aung-dev"
export PORT="8050"
export NEO4J_NAMESPACE="user_aung_dev"

echo "🚀 Starting Spark Memory MCP Server for user: aung-dev"
echo "📡 Server will be available at: http://192.168.1.84:8050"
echo "👤 User ID: $MCP_USER_ID"
echo "🔧 Using environment: .env.dev with user overrides"

# Start development server with user-specific settings
./dev.sh start