#!/bin/bash
# Start Spark Memory MCP Server for yohanna user

# Navigate to project directory
cd "$(dirname "$0")/.."

# Set user-specific environment variables
export MCP_USER_ID="yohanna"
export PORT="8051"
export NEO4J_NAMESPACE="user_yohanna"

echo "🚀 Starting Spark Memory MCP Server for user: yohanna"
echo "📡 Server will be available at: http://192.168.1.84:8051"
echo "👤 User ID: $MCP_USER_ID"
echo "🔧 Using environment: .env.dev with user overrides"

# Start development server with user-specific settings
./dev.sh start