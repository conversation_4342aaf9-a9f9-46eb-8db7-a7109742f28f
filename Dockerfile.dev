FROM python:3.12-slim

ARG PORT=8050

WORKDIR /app

# Install development dependencies
RUN apt-get update && \
    apt-get install -y \
    curl \
    git \
    vim \
    build-essential \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Install uv
RUN pip install uv

# Copy only requirements files first for better caching
COPY pyproject.toml .
COPY uv.lock* .

# Create virtual environment and install dependencies  
RUN python -m venv .venv
ENV PATH="/app/.venv/bin:$PATH"
RUN uv pip install -e . && \
    uv pip install pytest pytest-asyncio pytest-cov httpx

# Install development tools
RUN uv pip install \
    watchdog \
    ipython \
    ipdb \
    black \
    ruff \
    mypy

# Install GPU embedding dependencies
RUN uv pip install \
    torch \
    torchvision \
    torchaudio \
    --index-url https://download.pytorch.org/whl/cu118

# Install FlagEmbedding for direct BGE support
RUN uv pip install FlagEmbedding

EXPOSE ${PORT}

# Set Python path
ENV PYTHONPATH=/app:$PYTHONPATH

# Development command - run MCP server directly
CMD ["uv", "run", "python", "src/main_new.py"]
