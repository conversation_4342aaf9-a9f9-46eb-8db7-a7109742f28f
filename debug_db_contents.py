#!/usr/bin/env python3
"""
Debug script to check what's actually in the database
"""

import asyncio
import sys
import os
sys.path.append('src')

from supabase_mcp_integration import RealSupabaseMCP

async def debug_db_contents():
    """Check what's actually in the database"""
    print("🔍 Debugging Database Contents")
    print("=" * 40)
    
    try:
        supabase = RealSupabaseMCP()
        
        # Check all users in the database
        print(f"\n📋 Checking all users in database...")
        
        # First, get basic table stats
        all_memories_query = """
            SELECT user_id, COUNT(*) as memory_count, MAX(created_at) as latest_memory
            FROM memories 
            GROUP BY user_id 
            ORDER BY memory_count DESC
            LIMIT 10
        """
        
        user_stats = await supabase.execute_sql(all_memories_query, [])
        print(f"✅ Found {len(user_stats)} users with memories:")
        
        if user_stats:
            for stat in user_stats:
                user_id = stat.get('user_id', 'Unknown')
                count = stat.get('memory_count', 0)
                latest = stat.get('latest_memory', 'N/A')
                print(f"  - {user_id}: {count} memories (latest: {latest})")
                
            # Get some sample memories from the most active user
            top_user = user_stats[0].get('user_id')
            print(f"\n📋 Sample memories from user '{top_user}'...")
            
            sample_memories = await supabase.get_all_memories(top_user, limit=5)
            for i, memory in enumerate(sample_memories):
                content_preview = memory.get('content', '')[:60] + "..."
                created_at = memory.get('created_at', 'N/A')
                memory_id = memory.get('id', 'N/A')
                print(f"  {i+1}. ID {memory_id}: {content_preview} (Created: {created_at})")
        else:
            print("❌ No memories found in database!")
            
        # Check table structure
        print(f"\n📋 Checking table structure...")
        table_info_query = """
            SELECT column_name, data_type, is_nullable
            FROM information_schema.columns 
            WHERE table_name = 'memories'
            ORDER BY ordinal_position
        """
        
        columns = await supabase.execute_sql(table_info_query, [])
        if columns:
            print("✅ Memories table structure:")
            for col in columns:
                name = col.get('column_name', 'Unknown')
                dtype = col.get('data_type', 'Unknown')
                nullable = col.get('is_nullable', 'Unknown')
                print(f"  - {name}: {dtype} ({'nullable' if nullable == 'YES' else 'not null'})")
        
        # Check if there are any embedding vectors stored
        embedding_check_query = """
            SELECT COUNT(*) as count,
                   AVG(array_length(embedding::float[], 1)) as avg_dimensions
            FROM memories 
            WHERE embedding IS NOT NULL
        """
        
        embedding_stats = await supabase.execute_sql(embedding_check_query, [])
        if embedding_stats and embedding_stats[0]:
            count = embedding_stats[0].get('count', 0)
            avg_dims = embedding_stats[0].get('avg_dimensions', 'N/A')
            print(f"\n📊 Embedding statistics:")
            print(f"  - Memories with embeddings: {count}")
            print(f"  - Average dimensions: {avg_dims}")
        
    except Exception as e:
        print(f"❌ Debug failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(debug_db_contents())
