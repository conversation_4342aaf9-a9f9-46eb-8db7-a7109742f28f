# Production Environment Configuration for User 1 (aung-dev)
# WITH ISOLATED NEO4J DATABASE

# ==========================================
# User-Specific Configuration
# ==========================================
MCP_USER_ID=aung-dev
SERVER_PORT=8050
ENVIRONMENT=production

# ==========================================
# MCP Server Configuration
# ==========================================
TRANSPORT=sse
HOST=0.0.0.0
PORT=8050

# ==========================================
# LLM Configuration
# ==========================================
LLM_PROVIDER=openrouter
LLM_BASE_URL=https://openrouter.ai/api/v1
LLM_API_KEY=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
LLM_CHOICE=google/gemini-2.5-flash-lite

# ==========================================
# BGE Embedding Configuration
# ==========================================
BGE_SERVER_URL=http://************:8080

# ==========================================
# PRODUCTION Database Configuration (Supabase)
# ==========================================
DATABASE_URL=**************************************************************************/postgres

# ==========================================
# ISOLATED Neo4j Configuration for User 1
# ==========================================
NEO4J_URI=bolt://************:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=Ahayh@5096$
NEO4J_DATABASE=user1_aung_dev  # Isolated database

# ==========================================
# Performance and Monitoring
# ==========================================
LOCOMO_LATENCY_P50_TARGET=708
LOCOMO_LATENCY_P95_TARGET=1440
LOCOMO_TOKEN_TARGET=7000

# Production logging
LOG_LEVEL=INFO
PYTHONUNBUFFERED=1