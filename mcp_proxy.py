#!/usr/bin/env python3
"""
Simple MCP proxy for <PERSON> that doesn't require Node.js.
This script acts as a bridge between <PERSON> (stdio) and our SSE endpoint.
"""

import sys
import json
import requests
import threading
import time
import uuid
from queue import Queue, Empty

class MCPProxy:
    def __init__(self, sse_url):
        self.sse_url = sse_url
        self.session = requests.Session()
        self.message_queue = Queue()
        self.session_id = str(uuid.uuid4())
        
    def connect_sse(self):
        """Connect to SSE endpoint and listen for messages."""
        try:
            response = self.session.get(self.sse_url, stream=True)
            for line in response.iter_lines():
                if line:
                    line = line.decode('utf-8')
                    if line.startswith('data: '):
                        try:
                            data = json.loads(line[6:])  # Remove 'data: ' prefix
                            self.message_queue.put(data)
                        except json.JSONDecodeError:
                            pass
        except Exception as e:
            print(f"SSE connection error: {e}", file=sys.stderr)
    
    def send_http_message(self, message):
        """Send message via HTTP POST to /messages endpoint."""
        try:
            messages_url = self.sse_url.replace('/sse', '/messages/')
            if '?' not in messages_url:
                messages_url += f"?session_id={self.session_id}"
            
            response = self.session.post(messages_url, json=message)
            if response.status_code == 200:
                return response.json()
            else:
                return {"error": f"HTTP {response.status_code}: {response.text}"}
        except Exception as e:
            return {"error": str(e)}
    
    def run(self):
        """Main proxy loop."""
        # Start SSE listener in background
        sse_thread = threading.Thread(target=self.connect_sse, daemon=True)
        sse_thread.start()
        
        # Read from stdin and process MCP messages
        for line in sys.stdin:
            try:
                message = json.loads(line.strip())
                
                # Handle MCP protocol messages
                if message.get("method") == "initialize":
                    response = {
                        "jsonrpc": "2.0",
                        "id": message.get("id"),
                        "result": {
                            "protocolVersion": "2025-06-18",
                            "capabilities": {
                                "tools": {}
                            },
                            "serverInfo": {
                                "name": "spark-mcp-proxy",
                                "version": "1.0.0"
                            }
                        }
                    }
                    print(json.dumps(response))
                    sys.stdout.flush()
                
                else:
                    # Forward other messages to HTTP endpoint
                    result = self.send_http_message(message)
                    if message.get("id") is not None:
                        response = {
                            "jsonrpc": "2.0",
                            "id": message.get("id"),
                            "result": result
                        }
                        print(json.dumps(response))
                        sys.stdout.flush()
                        
            except json.JSONDecodeError:
                continue
            except Exception as e:
                error_response = {
                    "jsonrpc": "2.0",
                    "id": message.get("id") if 'message' in locals() else None,
                    "error": {
                        "code": -32603,
                        "message": str(e)
                    }
                }
                print(json.dumps(error_response))
                sys.stdout.flush()

if __name__ == "__main__":
    proxy = MCPProxy("http://localhost:8050/sse")
    proxy.run()
